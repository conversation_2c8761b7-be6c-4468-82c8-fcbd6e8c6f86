package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.Base62Util;
import cn.pinming.microservice.material.client.management.infrastructure.util.MurMurHashUtil;
import cn.pinming.microservice.material.client.management.common.mapper.ShortLinkMapper;
import cn.pinming.microservice.material.client.management.common.model.ShortLinkDO;
import cn.pinming.microservice.material.client.management.service.biz.IShortLinkService;
import cn.pinming.springboot.starter.redis.util.RedisUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * <p>
 * 短链接服务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Service
public class ShortLinkServiceImpl extends ServiceImpl<ShortLinkMapper, ShortLinkDO> implements IShortLinkService {

    @Resource
    private RedisUtil redisUtil;

    @Override
    public String getLongLink(String shortLink) {
        String longLink = redisUtil.get(shortLink, String.class);
        if (longLink == null) {
            LocalDateTime now = LocalDateTime.now();
            ShortLinkDO linkDO = lambdaQuery().eq(ShortLinkDO::getShortLink, shortLink).le(ShortLinkDO::getExpireTime, now).one();
            if (linkDO != null) {
                LocalDateTime expireTime = linkDO.getExpireTime();
                long seconds = LocalDateTimeUtil.between(now, expireTime, ChronoUnit.SECONDS);
                longLink = linkDO.getLongLink();
                redisUtil.set(shortLink, longLink, seconds);
            } else {
                redisUtil.set(shortLink, "", 60 * 60);
            }
        }
        return StrUtil.isBlank(longLink) ? null : longLink;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createShortLink(String longLink) {
        Long hash = MurMurHashUtil.hashUnsigned(longLink);
        String shortLink = Base62Util.toBase62(hash);

        // 判断是否存在
        ShortLinkDO linkDO = lambdaQuery().eq(ShortLinkDO::getShortLink, shortLink).one();
        ShortLinkDO link = new ShortLinkDO();
        if (linkDO != null) {
            long seconds = LocalDateTimeUtil.between(linkDO.getGmtModify(), LocalDateTime.now(), ChronoUnit.SECONDS);
            if (seconds < 60) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "请勿重复点击发送短信");
            }
            link.setId(linkDO.getId());
        } else {
            link.setLongLink(longLink);
            link.setShortLink(shortLink);
        }
        link.setExpireTime(LocalDateTime.now().plusDays(7));
        saveOrUpdate(link);

        // 缓存短链接7天
        redisUtil.set(shortLink, longLink, 60 * 60 * 24 * 7);
        return shortLink;
    }
}
