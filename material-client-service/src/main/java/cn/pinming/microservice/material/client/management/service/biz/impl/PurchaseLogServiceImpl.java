package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.mapper.PurchaseLogMapper;
import cn.pinming.microservice.material.client.management.common.model.PurchaseLogDO;
import cn.pinming.microservice.material.client.management.service.biz.PurchaseLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/8 10:07
 */
@Slf4j
@Service
public class PurchaseLogServiceImpl extends ServiceImpl<PurchaseLogMapper, PurchaseLogDO> implements PurchaseLogService {
}
