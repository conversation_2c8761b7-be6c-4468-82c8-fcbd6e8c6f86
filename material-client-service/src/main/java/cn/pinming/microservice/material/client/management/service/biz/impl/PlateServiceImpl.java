package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.common.dto.AnprDTO;
import cn.pinming.microservice.material.client.management.common.dto.PlateIdentifyDTO;
import cn.pinming.microservice.material.client.management.common.enums.RiskGradeEnum;
import cn.pinming.microservice.material.client.management.common.enums.WeighDataUpdateTypeEnum;
import cn.pinming.microservice.material.client.management.common.model.WeighDataDO;
import cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO;
import cn.pinming.microservice.material.client.management.common.vo.ocr.AnprRespVO;
import cn.pinming.microservice.material.client.management.common.vo.ocr.LPRRespVO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.MatchUtil;
import cn.pinming.microservice.material.client.management.service.biz.FileOssService;
import cn.pinming.microservice.material.client.management.service.biz.PlateService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataPicService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.pinming.microservice.material.client.management.infrastructure.constant.ConfigConstant.LRP_NAME;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/10/26 14:27
 */
@Slf4j
@Service
public class PlateServiceImpl implements PlateService {

    @Value("${material-ocr.lpr}")
    private String plateAnprUrl;
    @Resource
    private WeighDataPicService weighDataPicService;
    @Resource
    private FileOssService fileOssService;
    @Resource
    private WeighDataService weighDataService;

    @Override
    public List<String> identify(String recordId) {
        //查询称重记录下的照片
        List<WeighDataPicDO> dataList = weighDataPicService.lambdaQuery()
                .eq(WeighDataPicDO::getRecordId, recordId)
                .eq(WeighDataPicDO::getType, 1)
                .select(WeighDataPicDO::getFileId)
                .groupBy(WeighDataPicDO::getFilePath)
                .list();
        if (CollUtil.isEmpty(dataList)) {
            return Lists.emptyList();
        }
        //获取照片地址  先用中图转base64
        List<String> fileUrlList = dataList.stream()
                .map(obj -> fileOssService.getPicUrlByUuid(obj.getFileId(), (byte) 1)).collect(Collectors.toList());
        //调用识别服务
        return fileUrlList.parallelStream().map(this::getPlateNo).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    @Override
    public List<AnprRespVO.AnprVO> identifyByFileIds(String fileIds) {
        if (StrUtil.isBlank(fileIds)) {
            return Lists.emptyList();
        }
        //获取照片地址  先用中图转base64
        List<String> fileUrlList = StrUtil.split(fileIds, StrUtil.COMMA).stream()
                .map(fileId -> fileOssService.getPicUrlByUuid(fileId, (byte) 1)).collect(Collectors.toList());
        //调用识别服务
        return fileUrlList.parallelStream().map(this::getLprList).flatMap(Collection::stream).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlateAndRiskGrade(PlateIdentifyDTO identify, List<AnprDTO> anprList) {
        String lprTruckNo = identify.getLprTruckNo();
        if (StrUtil.isBlank(lprTruckNo)) {
            if (anprList.size() == 1) {
                anprListSingle(anprList, identify);
            } else {
                anprListMulti(anprList, identify);
            }
        } else {
            if (anprList.size() == 1) {
                String anprPlate = anprList.get(0).getPlateNo();
                if (anprPlate.equals(lprTruckNo)) {
                    identify.setNewRisk(RiskGradeEnum.HIGH.name());
                    identify.setAnprPlate(anprPlate);
                } else {
                    anprListSingle(anprList, identify);
                }
            } else {
                anprListMulti(anprList, identify);
            }
        }
        updateWeighDataAndSaveLog(identify);
    }

    public void anprListSingle(List<AnprDTO> anprList, PlateIdentifyDTO identify) {
        String anprPlate = anprList.get(0).getPlateNo();
        // 判断是否存在连续字符相同的情况
        String result = MatchUtil.plateSequenceEquals(identify.getTruckNo(), anprPlate);
        if (StrUtil.isNotBlank(result)) {
            identify.setNewRisk(RiskGradeEnum.LOW.name());
        } else {
            //判断连续相同字符 不考虑位置
            int count = MatchUtil.countCommonCharacters(identify.getTruckNo(), anprPlate);
            if (count >= 4) {
                identify.setNewRisk(RiskGradeEnum.LOW.name());
            } else if (count <= 2) {
                identify.setNewRisk(RiskGradeEnum.HIGH.name());
            } else {
                identify.setNewRisk(RiskGradeEnum.MIDDLE.name());
            }
        }
        identify.setAnprPlate(anprPlate);
    }

    public void anprListMulti(List<AnprDTO> anprList, PlateIdentifyDTO identify) {
        boolean isExist = Boolean.FALSE;
        Map<String, Integer> plateNoMap = new HashMap<>();
        for (AnprDTO anprDTO : anprList) {
            String plateNo = anprDTO.getPlateNo();
            String result = MatchUtil.plateSequenceEquals(identify.getTruckNo(), plateNo);
            if (StrUtil.isNotBlank(result)) {
                isExist = Boolean.TRUE;
            }
            int num = MatchUtil.countCommonCharacters(identify.getTruckNo(), plateNo);
            plateNoMap.put(plateNo, num);
        }

        if (isExist) { // 存在连续相同字符
            identify.setNewRisk(RiskGradeEnum.LOW.name());
        } else { //不存在连续相同字符
            String key = MatchUtil.getKeyWithMaxValue(plateNoMap);
            if (plateNoMap.get(key) >= 4) {
                identify.setNewRisk(RiskGradeEnum.LOW.name());
            } else {
                Optional<AnprDTO> maxConfidence = anprList.stream().max(Comparator.comparing(AnprDTO::getScore));
                if (maxConfidence.isPresent()) {
                    String anprPlate = maxConfidence.get().getPlateNo();
                    int num = MatchUtil.countCommonCharacters(identify.getTruckNo(), anprPlate);
                    if (num <= 2) {
                        identify.setNewRisk(RiskGradeEnum.HIGH.name());
                    } else {
                        identify.setNewRisk(RiskGradeEnum.MIDDLE.name());
                    }
                    identify.setAnprPlate(anprPlate);
                }
            }
        }
    }

    public void updateWeighDataAndSaveLog(PlateIdentifyDTO identify) {
        Long id = identify.getId();
        String oldRisk = identify.getOldRisk();
        String newRisk = identify.getNewRisk();
        String anprPlate = identify.getAnprPlate();
        String truckNo = identify.getTruckNo();
        String lprTruckNo = identify.getLprTruckNo();
        // 更新账号为 后台lpr程序
        if (Objects.equals(newRisk, RiskGradeEnum.LOW.name())) {
            log.info("action 1 不更新truckNo 将truckNo更新到lprTruckNo 低风险");
            weighDataService.lambdaUpdate()
                    .eq(WeighDataDO::getId, id)
                    .set(WeighDataDO::getLprTruckNo, truckNo)
                    .set(WeighDataDO::getRiskGrade, newRisk).update();
            weighDataService.weighDataLog(id, WeighDataUpdateTypeEnum.LPR_TRUCK_NO.name(), lprTruckNo, truckNo, LRP_NAME);
        } else if (Objects.equals(newRisk, RiskGradeEnum.MIDDLE.name())) {
            log.info("action 3 不更新truckNo 并将识别值更新到lprTruckNo 中风险");
            weighDataService.lambdaUpdate()
                    .eq(WeighDataDO::getId, id)
                    .set(WeighDataDO::getLprTruckNo, anprPlate)
                    .set(WeighDataDO::getRiskGrade, newRisk).update();
            weighDataService.weighDataLog(id, WeighDataUpdateTypeEnum.LPR_TRUCK_NO.name(), lprTruckNo, anprPlate, LRP_NAME);
        } else {
            log.info("action 2 将识别值更新到truckNo、lprTruckNo 高风险");
            weighDataService.lambdaUpdate()
                    .eq(WeighDataDO::getId, id)
                    .set(WeighDataDO::getTruckNo, anprPlate)
                    .set(WeighDataDO::getLprTruckNo, anprPlate)
                    .set(WeighDataDO::getRiskGrade, newRisk).update();
            weighDataService.weighDataLog(id, WeighDataUpdateTypeEnum.TRUCK_NO.name(), truckNo, anprPlate, LRP_NAME);
            weighDataService.weighDataLog(id, WeighDataUpdateTypeEnum.LPR_TRUCK_NO.name(), lprTruckNo, anprPlate, LRP_NAME);
        }
        // 更新风险等级日志
        weighDataService.weighDataLog(id, WeighDataUpdateTypeEnum.RISK_GRADE.name(), oldRisk, newRisk, LRP_NAME);
    }

    private String getPlateNo(String fileUrl) {
        List<AnprRespVO.AnprVO> anprList = getLprList(fileUrl);
        Optional<String> plateNo = anprList.parallelStream().map(t -> t.getRecognition().getPlateNo()).findAny();
        return plateNo.orElse(null);
    }

//    private List<AnprRespVO.AnprVO> getAnprList(String fileUrl) {
//        String body = "";
//        try {
//            Map<String, Object> map = new HashMap<>();
//            map.put("image", fileUrl);
//            map.put("limit", 1);
//            String url = plateAnprUrl + "/api/plate/recognition";
//            body = HttpRequest.post(url).body(JSONUtil.toJsonStr(map)).timeout(30 * 1000).execute().body();
//        } catch (Exception e) {
//            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "识别服务超时,请重试");
//        }
//        AnprRespVO anprRespVO = JSONUtil.toBean(body, AnprRespVO.class);
//        if (CollUtil.isEmpty(anprRespVO.getData())) {
//            return Lists.emptyList();
//        }
//        // 返回置信度大于等于 50 且车牌长度为7、8位的车牌数据
//        List<AnprRespVO.AnprVO> result = anprRespVO.getData().stream().filter(obj -> {
//            boolean isOk = NumberUtil.isGreaterOrEqual(obj.getScore(), new BigDecimal("50"));
//            int length = obj.getRecognition().getPlateNo().length();
//            return isOk && (length == 7 || length == 8);
//        }).collect(Collectors.toList());
//
//        return result;
//    }


    private List<AnprRespVO.AnprVO> getLprList(String fileUrl) {
        String body = "";
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("url", fileUrl);
            body = HttpRequest.post(plateAnprUrl).body(JSONUtil.toJsonStr(map)).timeout(30 * 1000).execute().body();
        } catch (Exception e) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "识别服务超时,请重试");
        }
        LPRRespVO lprRespVO = JSONUtil.toBean(body, LPRRespVO.class);
        if (!lprRespVO.getSuccess() || lprRespVO.getData() == null) {
            return Lists.emptyList();
        }
        // 返回置信度大于等于 50 且车牌长度为7、8位的车牌数据
        List<AnprRespVO.AnprVO> result = new ArrayList<>();
        String plateNo = lprRespVO.getData().getPlateNo();
        BigDecimal score = lprRespVO.getData().getScore();
        if (NumberUtil.isGreaterOrEqual(score, new BigDecimal("0.5")) && (plateNo.length() == 7 || plateNo.length() == 8)) {
            AnprRespVO.AnprVO anprVO = new AnprRespVO.AnprVO();
            AnprRespVO.AnprVO.RecognitionVO recognitionVO = new AnprRespVO.AnprVO.RecognitionVO();
            anprVO.setScore(score);
            recognitionVO.setPlateNo(plateNo);
            anprVO.setRecognition(recognitionVO);
            result.add(anprVO);
        }
        return result;
    }


//    public static void main(String[] args) {
//        String body = "";
//        try {
//            Map<String, Object> map = new HashMap<>();
//            map.put("url", "https://ossm.pinming.cn/1ab9e15b348348a8b91df55553004784/2025-03-19/b2a500b2-57e3-2c94-a05d-321d494f7f01.jpg?Expires=1744954366&OSSAccessKeyId=LTAI5t6AkVS76uhp9SCTxdHz&Signature=MqUH3vG04Zhh8lunW4TJK3a6gFA%3D&response-content-disposition=attachment%3Bfilename%3D%22104925.jpg%22");
//            String url = "localhost:8081/api/lpr";
//            body = HttpRequest.post(url).body(JSONUtil.toJsonStr(map)).timeout(30 * 1000).execute().body();
//        } catch (Exception e) {
//            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "识别服务超时,请重试");
//        }
//        LPRRespVO lprRespVO = JSONUtil.toBean(body, LPRRespVO.class);
//        System.out.println(lprRespVO);
//
//    }
}
