package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.dto.ocr.Point;
import cn.pinming.microservice.material.client.management.common.form.OCRModuleForm;
import cn.pinming.microservice.material.client.management.common.form.oss.OCRModuleDetailForm;
import cn.pinming.microservice.material.client.management.common.mapper.OcrModuleMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.OCRModuleExtMapper;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.common.query.OCRModuleQuery;
import cn.pinming.microservice.material.client.management.common.vo.OCRModuleDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.OCRModuleVO;
import cn.pinming.microservice.material.client.management.common.vo.ocr.*;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.client.management.service.biz.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OCRModuleServiceImpl extends ServiceImpl<OcrModuleMapper, OcrModuleDO> implements OCRModuleService {
    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private UserService userService;
    @Resource
    private OcrModuleDetailService ocrModuleDetailService;
    @Resource
    private OCRModuleExtMapper ocrModuleExtMapper;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private FileOssService fileOssService;
    @Resource
    private OCRResultService ocrResultService;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private UserExtConfigService userExtConfigService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addOCRModule(OCRModuleForm form) {
        // 主锚判断
        Byte billMatchType = form.getBillMatchType();
        if (billMatchType == 0) {
            mainCheck(form.getList(), null);
        }

        String uId = userIdUtil.getUId();
        UserDO userDO = userService.lambdaQuery().eq(UserDO::getUid, uId).one();
        if (userDO.getIsAdmin() == 1 && form.getType() == 5) {
            throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_IS_ERROR);
        }

        OcrModuleDO ocrModuleDO = new OcrModuleDO();
        ocrModuleDO.setName(form.getName());
        ocrModuleDO.setClient(form.getClient());
        ocrModuleDO.setPid(form.getPid());
        ocrModuleDO.setUid(userDO.getUid());
        ocrModuleDO.setType(form.getType());
        ocrModuleDO.setExtId(form.getExtId());
        ocrModuleDO.setBillMatchType(form.getBillMatchType());
        ocrModuleDO.setHtml(form.getHtml());
        if (StrUtil.isNotBlank(form.getBase64())) {
            String uuid = fileServiceProxy.uploadByBase64ForUuid(form.getBase64());
            ocrModuleDO.setFileId(uuid);
        }
        this.save(ocrModuleDO);

        // 数据组
        List<OCRModuleDetailForm> detailList = form.getList();
        Map<String, List<OCRModuleDetailForm>> groupMap = detailList.stream().collect(Collectors.groupingBy(OCRModuleDetailForm::getGroupName));
        List<OcrModuleDetailDO> result = new ArrayList<>();
        groupMap.forEach((k, v) -> {
            // 数据键名称重复检验
            moduleDetailCheck(v);

            List<OcrModuleDetailDO> collect = v.stream().map(e -> {
                OcrModuleDetailDO ocrModuleDetailDO = new OcrModuleDetailDO();
                BeanUtils.copyProperties(e, ocrModuleDetailDO);
                if (StrUtil.isNotBlank(e.getDeletedContent())) {
                    ocrModuleDetailDO.setDeletedContent(e.getDeletedContent().replaceAll(" ", ""));
                }
                ocrModuleDetailDO.setModuleId(ocrModuleDO.getId());
                return ocrModuleDetailDO;
            }).collect(Collectors.toList());
            String groupId = IdUtil.fastSimpleUUID();
            collect.forEach(e -> e.setGroupId(groupId));

            result.addAll(collect);
        });

        ocrModuleDetailService.saveBatch(result);
        return ocrModuleDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOCRModule(OCRModuleForm form) {
        if (ObjUtil.isNotNull(form.getId())) {

            Byte billMatchType = form.getBillMatchType();
            if (billMatchType == 0) {
                // 主锚校验
                mainCheck(form.getList(), form.getId());
            }

            OcrModuleDO ocrModuleDO = new OcrModuleDO();
            BeanUtils.copyProperties(form, ocrModuleDO);
            if (StrUtil.isNotBlank(form.getBase64())) {
                String uuid = fileServiceProxy.uploadByBase64ForUuid(form.getBase64());
                ocrModuleDO.setFileId(uuid);
            }
            this.updateById(ocrModuleDO);

            // 表单数据键id列表  ps:可能为空
            List<OCRModuleDetailForm> formList = form.getList();
            List<Long> formKeyIdList = formList.stream().map(OCRModuleDetailForm::getId).filter(Objects::nonNull).collect(Collectors.toList());

            // 数据库数据键id列表
            List<OcrModuleDetailDO> dbList = ocrModuleDetailService.lambdaQuery()
                    .eq(OcrModuleDetailDO::getModuleId, form.getId())
                    .list();
            List<Long> dbKeyIdList = dbList.stream().map(BaseDO::getId).collect(Collectors.toList());

            // 数据键业务
            List<OcrModuleDetailDO> result = new ArrayList<>();
            // 新增
            List<OCRModuleDetailForm> keyToAdd = formList.stream().filter(e -> e.getId() == null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(keyToAdd)) {
                Map<String, List<OCRModuleDetailForm>> keyToAddMap = keyToAdd.stream().collect(Collectors.groupingBy(OCRModuleDetailForm::getGroupName));
                keyToAddMap.forEach((k, v) -> {
                    // 新增键名重复校验
                    moduleDetailCheck(v);

                    String groupId = IdUtil.fastSimpleUUID();
                    List<OcrModuleDetailDO> keyToAddResult = v.stream().map(e -> {
                        OcrModuleDetailDO ocrModuleDetailDO = new OcrModuleDetailDO();
                        ocrModuleDetailDO.setModuleId(form.getId());
                        ocrModuleDetailDO.setGroupName(k);
                        ocrModuleDetailDO.setCoordinate(e.getCoordinate());
                        ocrModuleDetailDO.setGroupId(e.getGroupId());
                        ocrModuleDetailDO.setKeyName(e.getKeyName());
                        ocrModuleDetailDO.setValueName(e.getValueName());
                        ocrModuleDetailDO.setDataIndex(e.getDataIndex());
                        ocrModuleDetailDO.setType(e.getType());
                        if (StrUtil.isNotBlank(e.getDeletedContent())) {
                            ocrModuleDetailDO.setDeletedContent(e.getDeletedContent().replaceAll(" ", ""));
                        }
                        return ocrModuleDetailDO;
                    }).collect(Collectors.toList());
                    keyToAddResult.forEach(e -> {
                        if (StrUtil.isBlank(e.getGroupId())) {
                            e.setGroupId(groupId);
                        }
                    });
                    result.addAll(keyToAddResult);
                });
            }
            // 编辑、删除
            if (CollUtil.isNotEmpty(formKeyIdList)) {
                //编辑
                List<OCRModuleDetailForm> formKeyList = formList.stream().filter(e -> e.getId() != null).collect(Collectors.toList());
                List<OcrModuleDetailDO> keyToUpdateResult = formKeyList.stream().map(e -> {
                    OcrModuleDetailDO ocrModuleDetailDO = new OcrModuleDetailDO();
                    ocrModuleDetailDO.setId(e.getId());
                    ocrModuleDetailDO.setGroupName(e.getGroupName());
                    ocrModuleDetailDO.setKeyType(e.getKeyType());
                    ocrModuleDetailDO.setKeyName(e.getKeyName());
                    ocrModuleDetailDO.setCoordinate(e.getCoordinate());
                    ocrModuleDetailDO.setValueName(e.getValueName());
                    ocrModuleDetailDO.setDataIndex(e.getDataIndex());
                    ocrModuleDetailDO.setType(e.getType());
                    if (StrUtil.isNotBlank(e.getDeletedContent())) {
                        ocrModuleDetailDO.setDeletedContent(e.getDeletedContent().replaceAll(" ", ""));
                    }
                    return ocrModuleDetailDO;
                }).collect(Collectors.toList());
                result.addAll(keyToUpdateResult);

                // 删除
                List<Long> deleteList = dbKeyIdList.stream().filter(e -> !formKeyIdList.contains(e)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(deleteList)) {
                    ocrModuleDetailService.removeByIds(deleteList);
                }
            }
            if (CollUtil.isNotEmpty(result)) {
                ocrModuleDetailService.saveOrUpdateBatch(result);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshOCRModule(Long moduleId) {
        // 父类模版子项
        List<OcrModuleDetailDO> list = ocrModuleDetailService.lambdaQuery()
                .eq(OcrModuleDetailDO::getModuleId, moduleId)
                .list();
        if (CollUtil.isNotEmpty(list)) {
            List<OcrModuleDetailDO> result = new ArrayList<>();

            // 父类子项map
            Map<Long, OcrModuleDetailDO> pDataMap = list.stream().collect(Collectors.toMap(BaseDO::getId, e -> e));
            // 引用父类的模版
            List<OcrModuleDO> childrenList = this.lambdaQuery()
                    .eq(OcrModuleDO::getPid, moduleId)
                    .list();
            if (CollUtil.isNotEmpty(childrenList)) {
                // 引用父类的模版子项
                List<OcrModuleDetailDO> childrenDetailList = ocrModuleDetailService.lambdaQuery()
                        .in(OcrModuleDetailDO::getModuleId, childrenList.stream().map(BaseDO::getId).collect(Collectors.toList()))
                        .eq(OcrModuleDetailDO::getType, 3)
                        .list();
                Map<Long, List<OcrModuleDetailDO>> childrenDetailMap = childrenDetailList.stream().collect(Collectors.groupingBy(OcrModuleDetailDO::getModuleId));
                childrenDetailMap.forEach((k, v) -> {
                    List<Long> pIdList = list.stream().map(BaseDO::getId).collect(Collectors.toList());
                    List<Long> childrenIdList = v.stream().map(OcrModuleDetailDO::getPid).filter(Objects::nonNull).collect(Collectors.toList());
                    Map<String, String> childrenGroupMap = v.stream().collect(Collectors.groupingBy(OcrModuleDetailDO::getGroupName,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    sublist -> sublist.stream().findFirst().map(OcrModuleDetailDO::getGroupId).orElse(null)
                            )));
                    Map<String, String> childrenGroupAddMap = new HashMap<>();
                    // 新增
                    List<Long> add = pIdList.stream().filter(e -> !childrenIdList.contains(e)).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(add)) {
                        add.forEach(item -> {
                            OcrModuleDetailDO ocrModuleDetailDO = pDataMap.get(item);
                            ocrModuleDetailDO.setId(null);
                            ocrModuleDetailDO.setPid(item);
                            ocrModuleDetailDO.setModuleId(k);
                            String groupId = childrenGroupMap.get(ocrModuleDetailDO.getGroupName());
                            if (StrUtil.isBlank(groupId)) {
                                if (CollUtil.isNotEmpty(childrenGroupAddMap) && StrUtil.isNotBlank(childrenGroupAddMap.get(ocrModuleDetailDO.getGroupName()))) {
                                    // 父类创建了新组 && 子项之前创建过同名组
                                    groupId = childrenGroupAddMap.get(ocrModuleDetailDO.getGroupName());
                                } else {
                                    // 父类创建了新组 && 子项之前没创建过同名组
                                    groupId = IdUtil.fastSimpleUUID();
                                    childrenGroupAddMap.put(ocrModuleDetailDO.getGroupName(), groupId);
                                }
                            }
                            ocrModuleDetailDO.setGroupId(groupId);
                            result.add(ocrModuleDetailDO);
                        });
                    }

                    // 删除
                    List<Long> delete = v.stream().filter(e -> e.getPid() == null).map(OcrModuleDetailDO::getId).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(delete)) {
                        ocrModuleDetailService.removeByIds(delete);
                    }
                    List<Long> deleted = v.stream().filter(e -> e.getPid() != null && !pIdList.contains(e.getPid())).map(BaseDO::getId).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(deleted)) {
                        ocrModuleDetailService.removeByIds(deleted);
                    }

                    // 修改
                    List<Long> update = pIdList.stream().filter(childrenIdList::contains).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(update)) {
                        Map<Long, Long> childrenIdMap = v.stream().filter(e -> e.getPid() != null).collect(Collectors.toMap(OcrModuleDetailDO::getPid, BaseDO::getId));
                        update.forEach(item -> {
                            OcrModuleDetailDO newOcrModuleDetailDO = new OcrModuleDetailDO();
                            OcrModuleDetailDO ocrModuleDetailDO = pDataMap.get(item);
                            newOcrModuleDetailDO.setId(childrenIdMap.get(item));
                            newOcrModuleDetailDO.setGroupName(ocrModuleDetailDO.getGroupName());
                            newOcrModuleDetailDO.setKeyName(ocrModuleDetailDO.getKeyName());
                            newOcrModuleDetailDO.setKeyType(ocrModuleDetailDO.getKeyType());
                            result.add(newOcrModuleDetailDO);
                        });
                    }
                });

                // 落库
                ocrModuleDetailService.saveOrUpdateBatch(result);
            }
        }
    }

    @Override
    public List<OCRModuleVO> listOCRModule(OCRModuleQuery query) {
        List<OCRModuleVO> result = new ArrayList<>();
        query.setUid(userIdUtil.getUId());
        // 查询租户启用的模版类型
        UserExtConfigDO userExtConfigDO = userExtConfigService.lambdaQuery().eq(UserExtConfigDO::getUid, query.getUid()).one();
        if (ObjUtil.isNotNull(userExtConfigDO) && userExtConfigDO.getBillMatchType() != null) {
            query.setBillMatchType(userExtConfigDO.getBillMatchType());
        } else {
            query.setBillMatchType((byte) 0);
        }

        List<OcrModuleDO> list = ocrModuleExtMapper.selectList(query);
        if (CollUtil.isNotEmpty(list)) {
            List<Long> moduleIdList = list.stream().map(BaseDO::getId).collect(Collectors.toList());

            // 被引用的模版数量
            List<OcrModuleDO> childrenList = this.lambdaQuery()
                    .in(OcrModuleDO::getPid, moduleIdList)
                    .list();
            Map<Long, Long> childrenMap = new HashMap<>();
            if (CollUtil.isNotEmpty(childrenList)) {
                childrenMap = childrenList.stream().collect(Collectors.groupingBy(OcrModuleDO::getPid, Collectors.counting()));
            }

            Map<Long, Long> finalChildrenMap = childrenMap;
            list.forEach(e -> {
                OCRModuleVO ocrModuleVO = new OCRModuleVO();
                BeanUtils.copyProperties(e, ocrModuleVO);
                if (StrUtil.isNotBlank(e.getAttributionIds())) {
                    ocrModuleVO.setAttributionAmount(StrUtil.split(e.getAttributionIds(), ",").size());
                }
                if (CollUtil.isNotEmpty(finalChildrenMap) && finalChildrenMap.get(e.getId()) != null) {
                    ocrModuleVO.setChildrenAmount(finalChildrenMap.get(e.getId()));
                } else {
                    ocrModuleVO.setChildrenAmount(0L);
                }
                result.add(ocrModuleVO);
            });
        }
        return result;
    }

    @Override
    public OCRModuleVO detailOCRModule(Long moduleId) {
        OCRModuleVO vo = new OCRModuleVO();
        OcrModuleDO one = this.lambdaQuery().eq(BaseDO::getId, moduleId).one();
        if (ObjUtil.isNotNull(one)) {
            BeanUtils.copyProperties(one, vo);
            if (StrUtil.isNotBlank(one.getFileId())) {
                vo.setPic(fileOssService.getUrlByUuid(one.getFileId()));
            }
            OcrResultDO ocrResultDO = ocrResultService.lambdaQuery()
                    .eq(OcrResultDO::getModuleId, moduleId)
                    .one();
            if (ObjUtil.isNotNull(ocrResultDO)) {
                vo.setResult(ocrResultDO.getResult());
            }

            List<OcrModuleDetailDO> list = ocrModuleDetailService.lambdaQuery()
                    .eq(OcrModuleDetailDO::getModuleId, moduleId)
                    .list();
            List<OCRModuleDetailVO> detailVOList = list.stream().map(e -> {
                OCRModuleDetailVO ocrModuleDetailVO = new OCRModuleDetailVO();
                BeanUtils.copyProperties(e, ocrModuleDetailVO);
                return ocrModuleDetailVO;
            }).collect(Collectors.toList());
            vo.setList(detailVOList);
        }
        return vo;
    }

    @Override
    public List<OCRModuleVO> chooseOCRModule(OCRModuleQuery query) {
        List<OCRModuleVO> result = new ArrayList<>();

        query.setIsEnable((byte) 1);
        List<OcrModuleDO> list = ocrModuleExtMapper.selectList(query);
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(e -> {
                OCRModuleVO vo = new OCRModuleVO();
                BeanUtils.copyProperties(e, vo);
                result.add(vo);
            });
        }
        return result;
    }

    @Override
    public List<OCRModuleVO> allOCRModule(OCRModuleQuery query) {
        //查询列表
        query.setUid(userIdUtil.getUId());
        List<OcrModuleDO> list = ocrModuleExtMapper.selectList(query);
        if (CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }
        //查询详细
        Map<Long, List<OcrModuleDetailDO>> moduleDetailMap = ocrModuleDetailService.lambdaQuery()
                .in(OcrModuleDetailDO::getModuleId, list.stream().map(OcrModuleDO::getId).collect(Collectors.toSet()))
                .eq(OcrModuleDetailDO::getType, 3)
                .list().stream().collect(Collectors.groupingBy(OcrModuleDetailDO::getModuleId, Collectors.toList()));
        //组装返回
        return list.stream().map(e -> {
            OCRModuleVO vo = new OCRModuleVO();
            BeanUtils.copyProperties(e, vo);
            if (moduleDetailMap.containsKey(e.getId())) {
                vo.setList(moduleDetailMap.get(e.getId()).stream().map(d -> {
                    OCRModuleDetailVO ocrModuleDetailVO = new OCRModuleDetailVO();
                    BeanUtils.copyProperties(d, ocrModuleDetailVO);
                    return ocrModuleDetailVO;
                }).collect(Collectors.toList()));
            }
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<OCRVO> listModuleDetail(String uid, String attributionCode) {
//        log.error("uid:{},attributionCode:{}", uid, attributionCode);
        DeviceAttributionDO one = deviceAttributionService.lambdaQuery()
                .eq(DeviceAttributionDO::getUid, uid)
                .eq(StrUtil.isNotBlank(attributionCode) && attributionCode.contains(StrUtil.COMMA), DeviceAttributionDO::getCode, attributionCode)
                .apply(StrUtil.isNotBlank(attributionCode) && !attributionCode.contains(StrUtil.COMMA), "find_in_set({0},code)", attributionCode)
                .one();
        if (ObjUtil.isNull(one)) {
            throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_IS_EMPTY);
        }
        // 模版
        List<OCRConvertVO> convertList = ocrModuleExtMapper.selectModuleList(uid, one.getId(), (byte) 0);
        if (CollUtil.isEmpty(convertList)) {
            throw new BizErrorException(BizExceptionMessageEnum.MODULE_IS_NOT_EXIST);
        }
        List<OCRVO> list = convertList.stream().map(e -> {
            OCRVO vo = new OCRVO();
            vo.setId(e.getId());
            vo.setId_other(e.getExtId());
            return vo;
        }).collect(Collectors.toList());

        List<OCRVO> result = new ArrayList<>();
        List<String> moduleIdList = list.stream().map(OCRVO::getId).collect(Collectors.toList());
        Map<String, OCRVO> ocrvoMap = list.stream().collect(Collectors.toMap(OCRVO::getId, e -> e));
        // 模版子项
        List<OcrModuleDetailDO> detailList = ocrModuleDetailService.lambdaQuery()
                .in(OcrModuleDetailDO::getModuleId, moduleIdList)
                .list();
        Map<Long, List<OcrModuleDetailDO>> detailMap = detailList.stream().collect(Collectors.groupingBy(OcrModuleDetailDO::getModuleId));
        detailMap.forEach((k, v) -> {
            OCRVO ocrvo = new OCRVO();
            OCRVO vo = ocrvoMap.get(String.valueOf(k));
            ocrvo.setId(vo.getId());
            ocrvo.setId_other(vo.getId_other());

            // 主锚
            List<OcrModuleDetailDO> mainList = v.stream().filter(e -> e.getType() == (byte) 1).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(mainList)) {
                OcrModuleDetailDO main = mainList.get(0);
                RegionVO mainRegionVO = new RegionVO();
                mainRegionVO.setText(main.getKeyName());
                mainRegionVO.setRegion(pointConvert(main.getCoordinate()));
                ocrvo.setAnchor_area(mainRegionVO);
            }

            // 副锚
            List<OcrModuleDetailDO> backList = v.stream().filter(e -> e.getType() == (byte) 2).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(backList)) {
                OcrModuleDetailDO back = backList.get(0);
                RegionVO backRegionVO = new RegionVO();
                backRegionVO.setText(back.getKeyName());
                backRegionVO.setRegion(pointConvert(back.getCoordinate()));
                ocrvo.setBase_area(backRegionVO);
            }

            // 业务数据
            List<OcrModuleDetailDO> business = v.stream().filter(e -> e.getType() == (byte) 3).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(business)) {
                Map<String, List<OcrModuleDetailDO>> businessMap = business.stream().collect(Collectors.groupingBy(OcrModuleDetailDO::getGroupName));
                Map<String, Map<String, TextVO>> targets = new HashMap<>();
                businessMap.forEach((m, n) -> {
                    Map<String, TextVO> keyMap = new HashMap<>();
                    n.forEach(e -> {
                        TextVO textVO = new TextVO();
                        textVO.setRegion(pointConvert(e.getCoordinate()));
                        textVO.setText_do_delate(e.getDeletedContent());
                        keyMap.put(e.getKeyName(), textVO);
                    });
                    targets.put(m, keyMap);

                });
                ocrvo.setTargets(targets);
            }

            result.add(ocrvo);
        });
        return result;
    }

    @Override
    public List<OCRTemplateVO> listTemplateDetail(String uid, String attributionCode) {
        DeviceAttributionDO one = deviceAttributionService.lambdaQuery()
                .eq(DeviceAttributionDO::getUid, uid)
                .eq(StrUtil.isNotBlank(attributionCode) && attributionCode.contains(StrUtil.COMMA), DeviceAttributionDO::getCode, attributionCode)
                .apply(StrUtil.isNotBlank(attributionCode) && !attributionCode.contains(StrUtil.COMMA), "find_in_set({0},code)", attributionCode)
                .one();
        if (ObjUtil.isNull(one)) {
            throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_IS_EMPTY);
        }
        // 模版
        List<OCRConvertVO> convertList = ocrModuleExtMapper.selectModuleList(uid, one.getId(), (byte) 1);
        if (CollUtil.isEmpty(convertList)) {
            throw new BizErrorException(BizExceptionMessageEnum.MODULE_IS_NOT_EXIST);
        }
        Map<String, OCRConvertVO> ocrvoMap = convertList.stream().collect(Collectors.toMap(OCRConvertVO::getId, e -> e));
        // 模版子项
        List<OcrModuleDetailDO> detailList = ocrModuleDetailService.lambdaQuery().in(OcrModuleDetailDO::getModuleId, ocrvoMap.keySet()).list();
        Map<Long, List<OcrModuleDetailDO>> detailMap = detailList.stream().collect(Collectors.groupingBy(OcrModuleDetailDO::getModuleId));
        List<OCRTemplateVO> result = new ArrayList<>();
        detailMap.forEach((k, v) -> {
            OCRTemplateVO templateVO = new OCRTemplateVO();
            if (ocrvoMap.containsKey(String.valueOf(k))) {
                templateVO.setId(ocrvoMap.get(String.valueOf(k)).getId());
                templateVO.setExtId(ocrvoMap.get(String.valueOf(k)).getExtId());
            }

            List<ItemAreaVO> itemList = v.stream().map(obj -> {
                ItemAreaVO itemAreaVO = new ItemAreaVO();
                itemAreaVO.setType(obj.getType());
                itemAreaVO.setKey(obj.getKeyName());
                itemAreaVO.setValue(obj.getValueName());
                itemAreaVO.setIdx(obj.getDataIndex());
                itemAreaVO.setGroupName(obj.getGroupName());
                return itemAreaVO;
            }).collect(Collectors.toList());
            templateVO.setParam(itemList);
            result.add(templateVO);
        });

        return result;
    }

    /**
     * 校验数据组中数据键是否重复
     *
     * @param list
     */
    private void moduleDetailCheck(List<OCRModuleDetailForm> list) {
        int amount = list.size();
        int size = list.stream().collect(Collectors.groupingBy(OCRModuleDetailForm::getKeyName)).size();
        if (size != amount) {
            throw new BizErrorException(BizExceptionMessageEnum.MODULE_DETAIL_CAN_NOT_REPEAT);
        }
    }

    private List<double[]> pointConvert(String coordinate) {
        List<double[]> result = new ArrayList<>();
        Gson gson = new Gson();
        Point[] points = gson.fromJson(coordinate, Point[].class);
        if (points == null) {
            return result;
        }
        for (Point point : points) {
            double[] coordinates = {point.getX(), point.getY()};
            result.add(coordinates);
        }

        return result;
    }

    private void mainCheck(List<OCRModuleDetailForm> list, Long id) {
        List<OCRModuleDetailForm> mainPoint = list.stream().filter(e -> e.getType().equals((byte) 1)).collect(Collectors.toList());

        String uid = userIdUtil.getUId();
        // 新增
        if (CollUtil.isNotEmpty(mainPoint) && id == null) {
            if (mainPoint.size() > 1) {
                throw new BizErrorException(BizExceptionMessageEnum.MAIN_POINT_SIZE_ERROR);
            }

            String mainPointKeyName = mainPoint.get(0).getKeyName();

            int num = ocrModuleExtMapper.checkMainPoint(uid, id, mainPointKeyName);
            if (num > 0) {
                throw new BizErrorException(BizExceptionMessageEnum.MAIN_POINT_REPEAT);
            }

//            try {
//                ocrModuleDetailService.lambdaQuery()
//                        .eq(OcrModuleDetailDO::getKeyName, mainPointKeyName)
//                        .one();
//            } catch (Exception e) {
//                throw new BizErrorException(BizExceptionMessageEnum.MAIN_POINT_REPEAT);
//            }
        }
        // 编辑
        if (CollUtil.isNotEmpty(mainPoint) && id != null) {
            if (mainPoint.size() > 1) {
                throw new BizErrorException(BizExceptionMessageEnum.MAIN_POINT_SIZE_ERROR);
            }

            String mainPointKeyName = mainPoint.get(0).getKeyName();
            int num = ocrModuleExtMapper.checkMainPoint(uid, id, mainPointKeyName);
            if (num > 0) {
                throw new BizErrorException(BizExceptionMessageEnum.MAIN_POINT_REPEAT);
            }
//            try {
//                ocrModuleDetailService.lambdaQuery()
//                        .eq(OcrModuleDetailDO::getKeyName, mainPointKeyName)
//                        .ne(OcrModuleDetailDO::getModuleId, id)
//                        .one();
//            } catch (Exception e) {
//                throw new BizErrorException(BizExceptionMessageEnum.MAIN_POINT_REPEAT);
//            }
        }
    }
}
