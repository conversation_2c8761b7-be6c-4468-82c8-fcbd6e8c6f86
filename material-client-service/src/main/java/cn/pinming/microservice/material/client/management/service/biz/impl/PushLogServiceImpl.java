package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.mapper.PushLogMapper;
import cn.pinming.microservice.material.client.management.common.model.PushLogDO;
import cn.pinming.microservice.material.client.management.service.biz.PushLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class PushLogServiceImpl extends ServiceImpl<PushLogMapper, PushLogDO> implements PushLogService {
    @Resource
    private PushLogService pushLogService;

    @Override
    public void createLog(String uid, String body, Byte status, String reason) {
        PushLogDO pushLogDO = new PushLogDO();
        pushLogDO.setUid(uid);
        pushLogDO.setBody(body);
        pushLogDO.setStatus(status);
        pushLogDO.setReason(reason);

        pushLogService.save(pushLogDO);
    }

    @Override
    public void updateLog(String uid, String messageId, Byte status, String reason) {
        pushLogService.lambdaUpdate()
                .eq(PushLogDO::getUid, uid)
                .eq(messageId != null, PushLogDO::getMessageId, messageId)
                .set(PushLogDO::getStatus, status)
                .set(reason != null, PushLogDO::getReason, reason)
                .update();
    }
}
