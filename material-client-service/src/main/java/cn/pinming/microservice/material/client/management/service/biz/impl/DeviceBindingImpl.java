package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.Ipv4Util;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.enums.DeviceModeEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeviceTypeEnum;
import cn.pinming.microservice.material.client.management.common.form.BindingForm;
import cn.pinming.microservice.material.client.management.common.mapper.DeviceBindingMapper;
import cn.pinming.microservice.material.client.management.common.mapper.WeighCurveConfigMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceBindingExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.ReceiptRecycleExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataExtMapper;
import cn.pinming.microservice.material.client.management.common.model.DeviceBindingDO;
import cn.pinming.microservice.material.client.management.common.model.DeviceDO;
import cn.pinming.microservice.material.client.management.common.model.WeighCurveConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.DeviceAttributionVO;
import cn.pinming.microservice.material.client.management.common.vo.DeviceUserVO;
import cn.pinming.microservice.material.client.management.common.vo.selfcheck.SelfCheckModeConfigVO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.CheckUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.service.biz.DeviceBindingService;
import cn.pinming.microservice.material.client.management.service.biz.DeviceService;
import cn.pinming.microservice.material.client.management.service.biz.IWeighCurveConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

@Service
public class DeviceBindingImpl extends ServiceImpl<DeviceBindingMapper, DeviceBindingDO> implements DeviceBindingService {
    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private DeviceService deviceService;
    @Resource
    private DeviceBindingService deviceBindingService;
    @Resource
    private DeviceBindingExtMapper deviceBindingExtMapper;
    @Resource
    private CheckUtil checkUtil;
    @Resource
    private WeighDataExtMapper weighDataExtMapper;
    @Resource
    private ReceiptRecycleExtMapper receiptRecycleExtMapper;
    @Resource
    private IWeighCurveConfigService weighCurveConfigService;
    @Resource
    private WeighCurveConfigMapper weighCurveConfigMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void own(String deviceSn, String deviceType) {
        String uId = userIdUtil.getUId();
        // 准入校验
        DeviceDO deviceDO = deviceService.lambdaQuery()
                .eq(DeviceDO::getDeviceSn, deviceSn)
                .eq(DeviceDO::getDeviceType, deviceType)
                .one();
        if (ObjectUtil.isNull(deviceDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_NOT_EXIST);
        }
        // 占有校验
        checkUtil.deviceOccupiedCheck(deviceSn, deviceType);
        //保存
        DeviceBindingDO dto = new DeviceBindingDO();
        dto.setDeviceId(deviceDO.getId());
        dto.setUid(uId);
        deviceBindingService.save(dto);
    }

    @Override
    public void isReceive(Long id) {
        DeviceBindingDO one = deviceBindingService.lambdaQuery().eq(DeviceBindingDO::getId, id).one();
        if (ObjectUtil.isNotNull(one)) {
            deviceBindingService.lambdaUpdate()
                    .eq(DeviceBindingDO::getId, id)
                    .set(DeviceBindingDO::getReceive, one.getReceive() ^ 1)
                    .update();
        }
    }

    @Override
    public void binding(BindingForm form) {
        Long attributionId = form.getAttributionId();
        Long deviceId = form.getDeviceId();
        String password = form.getPassword();
        String auxiliaryCode = form.getAuxiliaryCode();
        String localIp = form.getLocalIp();
        if (StrUtil.isNotBlank(localIp) && !Ipv4Util.isInnerIP(localIp)) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(),"本地IP地址不合法");
        }

        String uId = userIdUtil.getUId();
        DeviceDO deviceDO = deviceService.lambdaQuery()
                .eq(DeviceDO::getId, deviceId)
                .one();
        if (ObjectUtil.isNull(deviceDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_NOT_EXIST);
        }
        // 占有校验
        Long id = checkUtil.deviceOccupiedByUid(deviceId, uId);
        // 绑定校验
        checkUtil.deviceAttributionCheck(deviceDO);
        DeviceBindingDO bindingDO = new DeviceBindingDO();
        bindingDO.setId(id);
        bindingDO.setDeviceName(form.getDeviceName());
        bindingDO.setAttributionId(attributionId);
        bindingDO.setBindingTime(LocalDateTime.now());
        if (StrUtil.isNotBlank(password)) {
            isSixDigitNumber(password);
            bindingDO.setPassword(password);
        }
        bindingDO.setLocalIp(form.getLocalIp());
        bindingDO.setAuxiliaryCode(auxiliaryCode);
        deviceBindingService.updateById(bindingDO);

        // 检查是否已存在配置记录，避免重复插入
        WeighCurveConfigDO existingConfig = weighCurveConfigMapper.getWeighCurveConfig(deviceId, attributionId, id);
        if (Objects.isNull(existingConfig)) {
            WeighCurveConfigDO weighCurveConfig = new WeighCurveConfigDO();
            weighCurveConfig.setUid(uId);
            weighCurveConfig.setAttributionId(attributionId);
            weighCurveConfig.setDeviceId(deviceId);
            weighCurveConfig.setDeviceBindingId(id); // 设置 deviceBindingId
            weighCurveConfigService.save(weighCurveConfig);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBinding(BindingForm form) {
        DeviceBindingDO deviceBindingDO = this.getById(form.getId());
        if (Objects.isNull(deviceBindingDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_HAS_NO_ATTRIBUTION);
        }

        String localIp = form.getLocalIp();
        if (StrUtil.isNotBlank(localIp) && !Ipv4Util.isInnerIP(localIp)) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(),"本地IP地址不合法");
        }
        isSixDigitNumber(form.getPassword());
        CopyOptions copyOptions = CopyOptions.create().ignoreNullValue();
        BeanUtil.copyProperties(form, deviceBindingDO, copyOptions);
        updateById(deviceBindingDO);

        WeighCurveConfigDO weighCurveConfig = weighCurveConfigMapper.getWeighCurveConfig(deviceBindingDO.getDeviceId(), deviceBindingDO.getAttributionId(), form.getId());
        if (weighCurveConfig == null) {
            weighCurveConfig = new WeighCurveConfigDO();
            BeanUtil.copyProperties(form, weighCurveConfig, "id");
            weighCurveConfig.setUid(deviceBindingDO.getUid());
            weighCurveConfig.setAttributionId(deviceBindingDO.getAttributionId());
            weighCurveConfig.setDeviceId(deviceBindingDO.getDeviceId());
            weighCurveConfig.setDeviceBindingId(form.getId()); // 设置 deviceBindingId
            weighCurveConfigService.save(weighCurveConfig);
        } else {
            BeanUtil.copyProperties(form, weighCurveConfig, "id");
            weighCurveConfig.setDeviceId(deviceBindingDO.getDeviceId());
            weighCurveConfigService.updateById(weighCurveConfig);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        deviceBindingService.lambdaUpdate().eq(DeviceBindingDO::getId, id)
                .set(DeviceBindingDO::getDeleted, 1)
                .update();

        weighCurveConfigService.lambdaUpdate().eq(WeighCurveConfigDO::getDeviceBindingId, id)
                .set(WeighCurveConfigDO::getDeleted, 1)
                .update();
    }

    @Override
    public void unbinding(Long id) {
        deviceBindingService.lambdaUpdate()
                .set(DeviceBindingDO::getAttributionId, null)
                .set(DeviceBindingDO::getBindingTime, null)
                .set(DeviceBindingDO::getPassword, null)
                .eq(DeviceBindingDO::getId, id)
                .update();
    }

    @Override
    public List<DeviceUserVO> userList() {
        String uId = userIdUtil.getUId();
        return deviceBindingExtMapper.userList(uId);
    }

    @Override
    public List<DeviceAttributionVO> attributionList(Long attributionId) {
        List<DeviceAttributionVO> list = deviceBindingExtMapper.attributionList(attributionId);
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        for (DeviceAttributionVO d : list) {
            if (d.getDeviceType().equals(DeviceTypeEnum.WEIGH.name())) {
                d.setNum(weighDataExtMapper.countByDeviceSn(d.getDeviceSn(), attributionId).intValue());
            } else if (d.getDeviceType().equals(DeviceTypeEnum.RECEIPT_RECYCLE.name())) {
                d.setNum(receiptRecycleExtMapper.countByDeviceSn(d.getDeviceSn(), attributionId).intValue());
            } else {
                d.setNum(0);
            }
            if (Objects.equals(d.getDeviceType(), DeviceTypeEnum.WEIGH.name())) {
                WeighCurveConfigDO weighCurveConfig = weighCurveConfigMapper.getWeighCurveConfig(d.getDeviceId(), d.getAttributionId(), d.getId());
                if (weighCurveConfig == null) {
                    weighCurveConfig = new WeighCurveConfigDO();
                    weighCurveConfig.setUid(d.getUid());
                    weighCurveConfig.setAttributionId(d.getAttributionId());
                    weighCurveConfig.setDeviceId(d.getDeviceId());
                    weighCurveConfig.setDeviceBindingId(d.getId());
                    weighCurveConfigService.save(weighCurveConfig);
                }
            }
        }
        return list;
    }

    @Override
    public String getDevicePwd(String sn, String deviceType) {
        DeviceDO deviceDO = deviceService.lambdaQuery().eq(DeviceDO::getDeviceSn, sn).eq(StrUtil.isNotBlank(deviceType), DeviceDO::getDeviceType, deviceType).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_NOT_EXIST));
        DeviceBindingDO deviceBindingDO = deviceBindingService.lambdaQuery().eq(DeviceBindingDO::getDeviceId, deviceDO.getId()).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.DEVICE_HAS_NO_ATTRIBUTION));
        return deviceBindingDO.getPassword();
    }

    @Override
    public Integer getDeviceMode(String deviceSn, String deviceType) {
        DeviceDO deviceDO = deviceService.lambdaQuery().eq(DeviceDO::getDeviceSn, deviceSn).eq(StrUtil.isNotBlank(deviceType), DeviceDO::getDeviceType, deviceType).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_NOT_EXIST));
        DeviceBindingDO deviceBindingDO = deviceBindingService.lambdaQuery().eq(DeviceBindingDO::getDeviceId, deviceDO.getId()).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.DEVICE_HAS_NO_ATTRIBUTION));
        return deviceBindingDO.getSelfCheckMode();
    }

    @Override
    public SelfCheckModeConfigVO getDeviceModeConfig(String deviceSn, String deviceType) {
        DeviceDO deviceDO = deviceService.lambdaQuery().eq(DeviceDO::getDeviceSn, deviceSn).eq(StrUtil.isNotBlank(deviceType), DeviceDO::getDeviceType, deviceType).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_NOT_EXIST));
        DeviceBindingDO deviceBindingDO = deviceBindingService.lambdaQuery().eq(DeviceBindingDO::getDeviceId, deviceDO.getId()).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.DEVICE_HAS_NO_ATTRIBUTION));
        SelfCheckModeConfigVO result = new SelfCheckModeConfigVO();
        result.setSelfCheckMode(deviceBindingDO.getSelfCheckMode());
        result.setSameTruckMinDuration(deviceBindingDO.getSameTruckMinDuration());
        result.setTimeout(deviceBindingDO.getTimeout());
        if (Objects.equals(deviceBindingDO.getSelfCheckMode(), DeviceModeEnum.FIVE.getVal())) {
            result.setAutoWeight(deviceBindingDO.getAutoWeight());
        }
        return result;
    }

    private void isSixDigitNumber(String input) {
        if (StrUtil.isBlank(input)) {
            return;
        }
        // 6位纯数字
        String regex = "\\d{6}";

        // 编译
        Pattern pattern = Pattern.compile(regex);

        // 匹配
        boolean matches = pattern.matcher(input).matches();
        if (!matches) {
            throw new BizErrorException(BizExceptionMessageEnum.PASSWORD_IS_ERROR);
        }
    }
}
