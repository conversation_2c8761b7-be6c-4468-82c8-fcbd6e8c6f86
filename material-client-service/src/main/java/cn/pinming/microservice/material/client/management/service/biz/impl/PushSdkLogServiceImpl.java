package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.mapper.PushSdkLogMapper;
import cn.pinming.microservice.material.client.management.common.model.PushSdkLogDO;
import cn.pinming.microservice.material.client.management.service.biz.IPushSdkLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * sdk推送日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
@Service
public class PushSdkLogServiceImpl extends ServiceImpl<PushSdkLogMapper, PushSdkLogDO> implements IPushSdkLogService {

}
