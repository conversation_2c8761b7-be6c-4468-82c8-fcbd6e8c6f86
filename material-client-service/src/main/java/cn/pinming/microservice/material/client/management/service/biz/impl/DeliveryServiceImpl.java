package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataCheckDTO;
import cn.pinming.microservice.material.client.management.common.enums.*;
import cn.pinming.microservice.material.client.management.common.form.*;
import cn.pinming.microservice.material.client.management.common.mapper.DeliveryDetailMapper;
import cn.pinming.microservice.material.client.management.common.mapper.DeliveryMapper;
import cn.pinming.microservice.material.client.management.common.mapper.PrintTemplateMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceAttributionExtMapper;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.common.model.ext.PrintTemplateExtDO;
import cn.pinming.microservice.material.client.management.common.model.ext.WeighDataConfirmOriginExtDO;
import cn.pinming.microservice.material.client.management.common.model.ext.WeighDataExtDO;
import cn.pinming.microservice.material.client.management.common.query.DeliveryPageQuery;
import cn.pinming.microservice.material.client.management.common.query.DeliveryQuery;
import cn.pinming.microservice.material.client.management.common.query.SelfCheckQuery;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryDetailDTO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryItemDTO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryTruckVO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryVO;
import cn.pinming.microservice.material.client.management.common.vo.h5.SimpleDeliveryDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.h5.SimpleDeliveryVO;
import cn.pinming.microservice.material.client.management.common.vo.selfcheck.CargoVO;
import cn.pinming.microservice.material.client.management.common.vo.selfcheck.PurchaseBaseInfoVO;
import cn.pinming.microservice.material.client.management.common.vo.selfcheck.SelfCheckDeliveryVO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.NoUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.service.biz.*;
import cn.pinming.v2.common.api.service.communication.provider.sms.AliyunSmsService;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 发货单(运单) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Slf4j
@Service
public class DeliveryServiceImpl extends ServiceImpl<DeliveryMapper, DeliveryDO> implements IDeliveryService {

    @Resource
    private DeliveryMapper deliveryMapper;
    @Resource
    private DeliveryDetailMapper deliveryDetailMapper;
    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private NoUtil noUtil;
    @Resource
    private IDeliveryDetailService deliveryDetailService;
    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private IPurchaseOrderDetailService purchaseOrderDetailService;
    @Resource
    private UserService userService;
    @Resource
    private WeighDataService weighDataService;
    @Resource
    private PrintTemplateMapper printTemplateMapper;
    @DubboReference
    private AliyunSmsService aliyunSmsService;
    @Resource
    private IShortLinkService shortLinkService;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private SupplierConfigService supplierConfigService;
    @Resource
    private DeviceAttributionExtMapper deviceAttributionExtMapper;
    @Resource
    private DeveloperService developerService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private DeviceBindingService deviceBindingService;
    @Resource
    private IWeighDataConfirmService weighDataConfirmService;


    @NacosValue(value = "${h5.info.url}", autoRefreshed = true)
    private String h5InfoUrl;
    @NacosValue(value = "${sms.enable}", autoRefreshed = true)
    private boolean smsEnable;


    @Override
    public List<DeliveryTruckVO> selectTruckList() {
        String uid = userIdUtil.getUId();
        return deliveryMapper.selectTruckList(uid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(DeliveryAddForm form) {
        Long orderId = form.getOrderId();
        boolean plateNumber = Validator.isPlateNumber(form.getTruckNo());
        if (!plateNumber) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "车牌号格式错误");
        }

        boolean mobile = Validator.isMobile(form.getDriverMobile());
        if (!mobile) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "手机号格式错误");
        }

        PurchaseOrderDO purchaseOrder = purchaseOrderService.getById(orderId);
        if (purchaseOrder == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "采购单不存在");
        }

        DeliveryDO delivery = new DeliveryDO();
        BeanUtil.copyProperties(purchaseOrder, delivery, "id", "createId", "gmtCreate", "modifyId", "gmtModify");
        delivery.setDriver(form.getDriver());
        delivery.setDriverMobile(form.getDriverMobile());
        delivery.setTruckNo(form.getTruckNo());
        delivery.setPurchaseOrderId(orderId);
        delivery.setNo(noUtil.generateUniqueId(1));
        delivery.setType(1);
        delivery.setCreateId(userIdUtil.getUId());
        delivery.setModifyId(userIdUtil.getUId());
        delivery.setPrintTemplateId(form.getPrintTemplateId());
        save(delivery);

        List<DeliveryDetailDO> list = form.getList().stream().map(item -> {
            DeliveryDetailDO detail = new DeliveryDetailDO();
            detail.setDeliveryId(delivery.getId());
            detail.setAmount(item.getAmount());
            detail.setPurchaseOrderDetailId(item.getOrderDetailId());
            return detail;
        }).collect(Collectors.toList());
        deliveryDetailService.saveBatch(list);

        // 发短信通知
        sendSms(form.getDriverMobile(), delivery.getDriver(), delivery.getId());

        // 修改采购单发货状态
        purchaseOrderService.lambdaUpdate()
                .eq(BaseDO::getId, delivery.getPurchaseOrderId())
                .set(PurchaseOrderDO::getDeliveryStatus, PurchaseDeliveryStatusEnum.TWO.value())
                .update();

        return delivery.getId();
    }

    @Override
    public Page<DeliveryVO> pageByQuery(DeliveryPageQuery query) {
        query.setUid(userIdUtil.getUId());
        return deliveryMapper.pageByQuery(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long deliveryId) {
        String uid = userIdUtil.getUId();
        DeliveryDO delivery = getById(deliveryId);
        if (delivery == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "发货单不存在");
        }
        if (!delivery.getCreateId().equals(uid)) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "非本租户发货单,无法作废");
        }
        if (delivery.getStatus() == 4) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "发货单已作废");
        }
        lambdaUpdate().eq(DeliveryDO::getId, deliveryId).eq(DeliveryDO::getCreateId, uid)
                .set(DeliveryDO::getStatus, 4).update(new DeliveryDO());
    }

    @Override
    public DeliveryDetailDTO detailByDeliveryId(Long deliveryId) {
        DeliveryDO delivery = getById(deliveryId);
        if (delivery == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "发货单不存在");
        }

        DeliveryDetailDTO result = new DeliveryDetailDTO();
        BeanUtils.copyProperties(delivery, result);
        Optional.ofNullable(userService.getByUid(delivery.getSupplierId())).ifPresent(t -> result.setSupplierName(t.getUserName()));

        List<DeliveryItemDTO> list = deliveryDetailMapper.selectListByDeliveryId(deliveryId);
        result.setList(list);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SelfCheckDeliveryVO getDeliveryDetail(SelfCheckQuery query) {
        String deviceSn = query.getDeviceSn();
        String deliveryNo = query.getDeliveryNo();
        WeighDataCheckDTO checkDTO = weighDataService.check(deviceSn, DeveloperAppEnum.QUERY.value(), false, DeviceTypeEnum.SELF_CHECK.name());
        String uid = checkDTO.getUid();
        Long deviceId = checkDTO.getDeviceId();
        DeliveryDO deliveryDO = lambdaQuery().eq(DeliveryDO::getNo, deliveryNo).eq(DeliveryDO::getAttributionId, checkDTO.getAttributionId())
                .oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "未查询到此运单，请使用正确的运单条码！"));

        // 组装返回数据
        SelfCheckDeliveryVO result = new SelfCheckDeliveryVO();
        result.setJsPreType(deliveryDO.getType());
        result.setNo(deliveryNo);
        result.setDriver(deliveryDO.getDriver());
        result.setTruckNo(deliveryDO.getTruckNo());
        result.setDriverMobile(deliveryDO.getDriverMobile());
        result.setDeliveryTime(LocalDateTimeUtil.format(deliveryDO.getGmtCreate(), DatePattern.NORM_DATETIME_PATTERN));

        if (deliveryDO.getType() == 1) {
            Long purchaseOrderId = deliveryDO.getPurchaseOrderId();

            PurchaseOrderDO purchaseOrderDO = deliveryMapper.getPurchaseOrder(purchaseOrderId, uid);
            if (purchaseOrderDO == null) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "非本项目运单,不可使用！");
            }

            if (deliveryDO.getStatus() == 4) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "此运单已作废，不可使用！");
            }

            // 查询该设备id对应的打印模版
//            PrintTemplateDO template = printTemplateMapper.selectByDeviceId(deviceId, uid, TemplateTypeEnum.DRIVER_CHECK_ORDER.getVal());
//            if (template != null) {
//                result.setPrintLimit(template.getPrintLimit());
//                result.setPrintTemplate(JSONUtil.parseObj(template.getContent()));
//            }

            List<PrintTemplateDO> templateList = printTemplateMapper.selectByDeviceIdAndStyle(deviceId, uid,
                    TemplateTypeEnum.DRIVER_CHECK_ORDER.getVal(), null, TemplateFormTypeEnum.THREE.getVal());
            if (CollUtil.isNotEmpty(templateList)) {
                PrintTemplateDO template = templateList.get(0);
                result.setPrintLimit(template.getPrintLimit());
                result.setPrintTemplate(JSONUtil.parseObj(template.getContent()));
            }

            SupplierConfigDO supplierConfigDO = supplierConfigService.lambdaQuery().eq(SupplierConfigDO::getSupplierExtId, deliveryDO.getSupplierExtId())
                    .eq(SupplierConfigDO::getCreateId, uid).one();

            PurchaseBaseInfoVO purchaseBaseInfo = new PurchaseBaseInfoVO();
            purchaseBaseInfo.setId(deliveryDO.getPurchaseOrderId());
            purchaseBaseInfo.setPurchaseId(deliveryDO.getOrderExtId());
            purchaseBaseInfo.setSupplierId(deliveryDO.getSupplierId());
            if (supplierConfigDO != null) {
                purchaseBaseInfo.setSupplierName(supplierConfigDO.getName());
            }
            purchaseBaseInfo.setOrderName(deliveryDO.getProject());
            purchaseBaseInfo.setOrderAddress(deliveryDO.getAddress());
            purchaseBaseInfo.setCargoReceiver(deliveryDO.getReceiver());
            purchaseBaseInfo.setReceiverTelNumber(deliveryDO.getMobile());
            purchaseBaseInfo.setRequireDate(deliveryDO.getReceiveDate().toString());
            purchaseBaseInfo.setRemark(deliveryDO.getRemark());
            DeviceAttributionDO attributionDO = deviceAttributionService.getById(purchaseOrderDO.getAttributionId());
            if (attributionDO != null) {
                purchaseBaseInfo.setAttributionName(attributionDO.getName());
            }
            result.setPurchaseBaseInfo(purchaseBaseInfo);

            List<CargoVO> cargoList = deliveryDetailMapper.selectCargoListByDeliveryId(Collections.singletonList(deliveryDO.getId()));
            if (CollUtil.isNotEmpty(cargoList)) {
                //该运单中所有货物明细，无“在途”及“待确认“状态，且至少存在一个“自助准认中”状态时 此运单货物当前正在到场确认中，不可重复使用！
                List<CargoVO> filterList = cargoList.stream().filter(item -> item.getStatus() != 1 && item.getStatus() != 2).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(filterList)) {
                    filterList.stream().filter(item -> item.getStatus() == 3).findAny().ifPresent(item -> {
                        throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "该运单货物当前正在到场确认中，不可重复使用！");
                    });
                }

                boolean isAllChecked = cargoList.stream().allMatch(item -> item.getStatus() == 4);
                if (isAllChecked) {
                    throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "此运单货物均已完成到场确认，不可重复使用！");
                }

                cargoList = cargoList.stream().filter(item -> item.getStatus() == 1 || item.getStatus() == 2).collect(Collectors.toList());
                result.setCargoList(cargoList);
                List<Long> cargoIdList = cargoList.stream().map(CargoVO::getId).collect(Collectors.toList());
                deliveryDetailService.lambdaUpdate().set(DeliveryDetailDO::getStatus, 3).in(DeliveryDetailDO::getId, cargoIdList).update();
            }
        } else if (deliveryDO.getType() == 2) {
            if (deliveryDO.getStatus() == 4) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "此运单已作废，不可使用！");
            }
            if (deliveryDO.getStatus() != 1) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "此运单已被使用！");
            }
            // 查询该设备id对应的打印模版
            List<PrintTemplateDO> templateList = printTemplateMapper.selectByDeviceIdAndStyle(deviceId, uid,
                    TemplateTypeEnum.DELIVERY_OCR_ORDER.getVal(), null, TemplateFormTypeEnum.THREE.getVal());
            if (CollUtil.isNotEmpty(templateList)) {
                PrintTemplateDO template = templateList.get(0);
                result.setPrintLimit(template.getPrintLimit());
                result.setPrintTemplate(JSONUtil.parseObj(template.getContent()));
            }

            Long attributionId = deliveryDO.getAttributionId();
            if (attributionId != null) {
                DeviceAttributionDO attributionDO = deviceAttributionService.getById(attributionId);
                if (attributionDO != null) {
                    PurchaseBaseInfoVO purchaseBaseInfo = new PurchaseBaseInfoVO();
                    purchaseBaseInfo.setAttributionName(attributionDO.getName());
                    result.setPurchaseBaseInfo(purchaseBaseInfo);
                }
            }
        }
        // 修改发货单状态
        lambdaUpdate().eq(DeliveryDO::getId, deliveryDO.getId()).eq(DeliveryDO::getStatus, 1).set(DeliveryDO::getStatus, 2).update();
        return result;
    }

    @Override
    public Long h5Add(DeliveryAddSelfCheckForm form) {
        Long orderId = form.getOrderId();
        Long attributionId = form.getAttributionId();
        if (attributionId == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方id为空");
        }
        DeviceAttributionDO attributionDO = deviceAttributionService.getById(attributionId);
        if (attributionDO == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方不存在");
        }
//        boolean plateNumber = Validator.isPlateNumber(form.getTruckNo());
//        if (!plateNumber) {
//            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "车牌号格式错误");
//        }
//
//        boolean mobile = Validator.isMobile(form.getDriverMobile());
//        if (!mobile) {
//            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "手机号格式错误");
//        }

        PurchaseOrderDO purchaseOrder = purchaseOrderService.getById(orderId);
        if (purchaseOrder == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "采购单不存在");
        }

        DeliveryDO delivery = new DeliveryDO();
        BeanUtil.copyProperties(purchaseOrder, delivery, "id", "createId", "gmtCreate", "modifyId", "gmtModify");
//        delivery.setDriver(form.getDriver());
//        delivery.setDriverMobile(form.getDriverMobile());
//        delivery.setTruckNo(form.getTruckNo());
        delivery.setPurchaseOrderId(orderId);
        delivery.setNo(noUtil.generateUniqueId(1));
        delivery.setType(1);
        delivery.setCreateId(attributionDO.getUid());
        delivery.setModifyId(attributionDO.getUid());
        save(delivery);

        List<DeliveryDetailDO> list = form.getList().stream().map(item -> {
            DeliveryDetailDO detail = new DeliveryDetailDO();
            detail.setDeliveryId(delivery.getId());
            detail.setAmount(item.getAmount());
            detail.setPurchaseOrderDetailId(item.getOrderDetailId());
            detail.setCreateId(attributionDO.getUid());
            detail.setModifyId(attributionDO.getUid());
            return detail;
        }).collect(Collectors.toList());
        deliveryDetailService.saveBatch(list);
        // 发短信通知
//        sendSms(form.getDriverMobile(), delivery.getDriver(), delivery.getId());

        // 修改采购单发货状态
        purchaseOrderService.lambdaUpdate()
                .eq(BaseDO::getId, delivery.getPurchaseOrderId())
                .set(PurchaseOrderDO::getDeliveryStatus, PurchaseDeliveryStatusEnum.TWO.value())
                .update();

        return delivery.getId();
    }

    @Override
    public String selfCheckAdd(DeliveryAddSelfCheckForm form) {
        Long orderId = form.getOrderId();
        Long attributionId = form.getAttributionId();
        if (attributionId == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方id为空");
        }
        DeviceAttributionDO attributionDO = deviceAttributionService.getById(attributionId);
        if (attributionDO == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方不存在");
        }

        PurchaseOrderDO purchaseOrder = purchaseOrderService.getById(orderId);
        if (purchaseOrder == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "采购单不存在");
        }

        DeliveryDO delivery = new DeliveryDO();
        BeanUtil.copyProperties(purchaseOrder, delivery, "id", "createId", "gmtCreate", "modifyId", "gmtModify");
        delivery.setPurchaseOrderId(orderId);
        delivery.setNo(noUtil.generateUniqueId(1));
        delivery.setType(1);
        delivery.setCreateId(attributionDO.getUid());
        delivery.setModifyId(attributionDO.getUid());
        delivery.setTruckNo(form.getTruckNo());
        save(delivery);

        List<DeliveryDetailDO> list = form.getList().stream().map(item -> {
            DeliveryDetailDO detail = new DeliveryDetailDO();
            detail.setDeliveryId(delivery.getId());
            detail.setAmount(item.getAmount());
            detail.setPurchaseOrderDetailId(item.getOrderDetailId());
            detail.setCreateId(attributionDO.getUid());
            detail.setModifyId(attributionDO.getUid());
            return detail;
        }).collect(Collectors.toList());
        deliveryDetailService.saveBatch(list);
        // 修改采购单发货状态
        purchaseOrderService.lambdaUpdate()
                .eq(BaseDO::getId, delivery.getPurchaseOrderId())
                .set(PurchaseOrderDO::getDeliveryStatus, PurchaseDeliveryStatusEnum.TWO.value())
                .update();
        return delivery.getNo();
    }


    @SneakyThrows
    private void sendSms(String mobile, String name, Long deliveryId) {
        if (StrUtil.isBlank(mobile)) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "司机手机号为空");
        }

        String longLink = StrUtil.format(h5InfoUrl, deliveryId);
        String shortLink = shortLinkService.createShortLink(longLink);
        if (smsEnable) {
            Map<String, String> map = new HashMap<>();
            map.put("name", name);
            map.put("code", StrUtil.format("s/{}", shortLink));
            aliyunSmsService.send(mobile, "SMS_472425158", map);
        }
    }

    @Override
    public SimpleDeliveryVO h5DetailByDeliveryId(Long deliveryId) {
        DeliveryDO deliveryDO = getById(deliveryId);
        if (deliveryDO == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "运单不存在");
        }
        SimpleDeliveryVO result = new SimpleDeliveryVO();
        BeanUtils.copyProperties(deliveryDO, result);
        PurchaseOrderDO purchaseOrderDO = purchaseOrderService.getById(deliveryDO.getPurchaseOrderId());
        result.setOwner(purchaseOrderDO.getProject());

        List<DeliveryItemDTO> list = deliveryDetailMapper.selectListByDeliveryId(deliveryId);
        if (CollUtil.isNotEmpty(list)) {
            List<SimpleDeliveryDetailVO> detailList = list.stream().map(item -> {
                SimpleDeliveryDetailVO detail = new SimpleDeliveryDetailVO();
                detail.setName(item.getName());
                detail.setAmount(item.getCurrentAmount());
                detail.setSpec(item.getSpec());
                detail.setArgument(item.getArgument());
                detail.setUnit(item.getUnit());
                detail.setPosition(item.getPosition());
                return detail;
            }).collect(Collectors.toList());
            result.setList(detailList);
        }
        return result;
    }

    @Override
    public void sendDeliverySms(Long deliveryId) {
        DeliveryDO deliveryDO = getById(deliveryId);
        if (deliveryDO == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "运单不存在");
        }
        sendSms(deliveryDO.getDriverMobile(), deliveryDO.getDriver(), deliveryId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deliveryAdd(Long attributionId, Integer type, String truckNo) {
        if (attributionId == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方id为空");
        }
        DeviceAttributionDO attributionDO = deviceAttributionService.getById(attributionId);
        if (attributionDO == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方不存在");
        }

        DeliveryDO delivery = new DeliveryDO();
        delivery.setNo(noUtil.generateUniqueId(type));
        delivery.setAttributionId(attributionId);
        delivery.setType(type);
        delivery.setCreateId(attributionDO.getUid());
        delivery.setModifyId(attributionDO.getUid());
        delivery.setTruckNo(truckNo);
        save(delivery);
        log.info("【归属方新增发货单】delivery : {}",JSONUtil.toJsonStr(delivery));
        return delivery.getNo();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String selfCheckOCRAdd(String deviceSn, String deviceType) {
        DeviceDO deviceDO = getDeviceDO(deviceSn, deviceType);
        DeviceBindingDO deviceBindingDO = getDeviceBindingDO(deviceDO);
        DeviceAttributionDO attribution = getDeviceAttributionDO(deviceBindingDO);
        return deliveryAdd(attribution.getId(), 2, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String selfCheckWeighAdd(String deviceSn, String deviceType, String truckNo) {
        DeviceDO deviceDO = getDeviceDO(deviceSn, deviceType);
        DeviceBindingDO deviceBindingDO = getDeviceBindingDO(deviceDO);
        DeviceAttributionDO attribution = getDeviceAttributionDO(deviceBindingDO);
        return deliveryAdd(attribution.getId(), 3, truckNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDriverMobile(Long deliveryId, String mobile) {
        DeliveryDO deliveryDO = getById(deliveryId);
        if (deliveryDO == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "运单不存在");
        }
        boolean isMobile = Validator.isMobile(mobile);
        if (!isMobile) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "手机号格式错误");
        }
        lambdaUpdate().eq(DeliveryDO::getId, deliveryId).set(DeliveryDO::getDriverMobile, mobile).update();
    }

    @Override
    public String deliveryAdd(DeliverySyncForm form, String appKeyHeader) {
        DeliverySyncIdentityForm jsInfo = form.getJsInfo();
        DeliverySyncPurchaseForm purchaseBaseInfo = form.getPurchaseBaseInfo();
        List<DeliverySyncMaterialForm> cargoList = form.getCargoList();

        UserDO userDO = userService.getUserByAppKey(appKeyHeader);
        DeveloperDO developerDO = developerService.lambdaQuery()
                .eq(BaseDO::getCreateId, userDO.getUid())
                .eq(DeveloperDO::getAppId, DeveloperAppEnum.DELIVERY_SYNC.value())
                .one();
        if (ObjUtil.isNull(developerDO) || developerDO.getType().equals(DeveloperTypeEnum.STOP.value())) {
            throw new BizErrorException(BizExceptionMessageEnum.SERVICE_DISABLED, DeveloperAppEnum.DELIVERY_SYNC.description());
        }
        DeviceAttributionDO attributionDO = deviceAttributionService.lambdaQuery()
                .eq(DeviceAttributionDO::getUid, jsInfo.getJsTenantId())
                .apply("find_in_set({0},code)", jsInfo.getJsAttributionCode())
                .one();
        if (ObjUtil.isNull(attributionDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_IS_EMPTY);
        }
        if (StrUtil.isBlank(purchaseBaseInfo.getPurchaseId()) && StrUtil.isBlank(purchaseBaseInfo.getJsBillOfParcelsNumber())) {
            throw new BizErrorException(BizExceptionMessageEnum.ORDER_IS_EMPTY);
        }
        String no = deliveryNoCheck(form.getJsDeliveryNo(), form.getJsInfo(), attributionDO.getId());

        // purchaseBaseInfo.getPurchaseId()是客户的采购单id
        PurchaseOrderDO purchaseOrderDO = purchaseOrderService.lambdaQuery()
                .eq(PurchaseOrderDO::getOrderExtId, purchaseBaseInfo.getPurchaseId())
                .one();
        if (ObjUtil.isNull(purchaseOrderDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.ORDER_IS_NOT_EXITS, purchaseBaseInfo.getPurchaseId(), jsInfo.getJsTenantId(), attributionDO.getId());
        }
        List<PurchaseOrderDetailDO> detailList = purchaseOrderDetailService.lambdaQuery()
                .eq(PurchaseOrderDetailDO::getOrderId, purchaseOrderDO.getId())
                .list();
        Map<String, Long> detailDOMap = detailList.stream().collect(Collectors.toMap(PurchaseOrderDetailDO::getExtId, PurchaseOrderDetailDO::getId));
        DeliveryDO deliveryDO = new DeliveryDO();
        deliveryDO.setNo(no);
        deliveryDO.setType(1);
        deliveryDO.setAttributionId(attributionDO.getId());
        deliveryDO.setPurchaseOrderId(purchaseOrderDO.getId());
        deliveryDO.setTruckNo(form.getTruckNo());
        deliveryDO.setDriver(form.getDriverName());
        deliveryDO.setDriverMobile(form.getDriverPhoneNo());
        deliveryDO.setSourceType(1);
        deliveryDO.setOrderExtId(purchaseBaseInfo.getPurchaseId());
        deliveryDO.setSupplierExtId(purchaseOrderDO.getSupplierExtId());
        deliveryDO.setSupplierId(purchaseOrderDO.getSupplierId());
        deliveryDO.setProject(purchaseBaseInfo.getOrderName());
        deliveryDO.setAddress(purchaseBaseInfo.getOrderAddress());
        deliveryDO.setReceiver(purchaseBaseInfo.getCargoReceiver());
        deliveryDO.setMobile(purchaseBaseInfo.getReceiverTelNumber());
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime parse = LocalDateTime.parse(purchaseBaseInfo.getRequireDate(), dateTimeFormatter);
        deliveryDO.setReceiveDate(parse.toLocalDate());
        deliveryDO.setRemark(purchaseBaseInfo.getRemark());
        this.save(deliveryDO);

        List<DeliveryDetailDO> detailDOList = cargoList.stream().map(e -> {
            DeliveryDetailDO detailDO = new DeliveryDetailDO();
            detailDO.setPurchaseOrderDetailId(detailDOMap.get(e.getCargoId()));
            detailDO.setDeliveryId(deliveryDO.getId());
            detailDO.setAmount(e.getWaybillCounts());

            return detailDO;
        }).collect(Collectors.toList());
        deliveryDetailService.saveBatch(detailDOList);

        // 发送短信
        if (!no.equals(form.getJsDeliveryNo())) {
            sendDeliverySms(deliveryDO.getId());
        }

        return no;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public WeighDataConfirmOriginExtDO delivery(DeliveryQuery query) {
        String deviceSn = query.getDeviceSn();
        String deviceType = query.getDeviceType();
        Integer mode = query.getMode();
        String truckNo = query.getTruckNo();

        DeviceDO deviceDO = getDeviceDO(deviceSn, deviceType);
        DeviceBindingDO deviceBindingDO = getDeviceBindingDO(deviceDO);
        DeviceAttributionDO attribution = getDeviceAttributionDO(deviceBindingDO);

        WeighDataConfirmOriginExtDO result = new WeighDataConfirmOriginExtDO();
        if (checkTruckNo(truckNo) && Objects.equals(mode, DeviceModeEnum.ONE.getVal())) {
            DeliveryDO deliveryDO = getDeliveryDO(attribution, truckNo, 1);
            Integer timeout = 0;
            if (deliveryDO != null && deliveryDO.getStatus().equals(DeliveryStatusEnum.FIVE.value())) {
                // 查询确认单数据
                WeighDataConfirmDO confirmDO = getWeighDataConfirmDO(attribution, deviceSn, truckNo);

                if (confirmDO != null) {
                    String supplierId = deliveryDO.getSupplierExtId();
                    String createId = attribution.getCreateId();
                    SupplierConfigDO supplierConfigDO = supplierConfigService.lambdaQuery().eq(SupplierConfigDO::getSupplierExtId, supplierId).eq(SupplierConfigDO::getCreateId, createId)
                            .oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "外部供应商id未绑定"));
                    timeout = supplierConfigDO.getTimeout();
                    result = getWeighDataConfirmOriginExtDO(confirmDO, deliveryDO, timeout);
                }
            }
            result.setTimeoutMinutes(timeout);
            result.setSignature(deviceBindingDO.getSignature());
            result.setSkipScanCode(deviceBindingDO.getSkipScanCode());
            result.setDeviceAttribution(attribution.getName());
            result.setPrintTemplateList(queryPrintTemplateList(deviceDO, attribution, TemplateTypeEnum.DRIVER_CHECK_ORDER.getVal()));
        } else if (checkTruckNo(truckNo) && (Objects.equals(mode, DeviceModeEnum.TWO.getVal()) || Objects.equals(mode, DeviceModeEnum.FIVE.getVal()))) {
            int type = mode == 2 ? 2 : 3;
            byte templateType = mode == 2 ? TemplateTypeEnum.DELIVERY_OCR_ORDER.getVal() : TemplateTypeEnum.DRIVER_CHECK_ORDER.getVal();

            DeliveryDO deliveryDO = getDeliveryDO(attribution, truckNo, type);
            if (deliveryDO != null && deliveryDO.getStatus().equals(DeliveryStatusEnum.FIVE.value())) {
                // 查询确认单数据
                WeighDataConfirmDO confirmDO = getWeighDataConfirmDO(attribution, deviceSn, truckNo);
                if (confirmDO != null) {
                    result = getWeighDataConfirmOriginExtDO(confirmDO, deliveryDO, deviceBindingDO.getTimeout());
                }
            }
            result.setTimeoutMinutes(deviceBindingDO.getTimeout());
            result.setSignature(deviceBindingDO.getSignature());
            result.setDeviceAttribution(attribution.getName());
            result.setPrintTemplateList(queryPrintTemplateList(deviceDO, attribution, templateType));
        } else if (Objects.equals(mode, DeviceModeEnum.SIX.getVal())) {
            result.setPrintTemplateList(queryPrintTemplateList(deviceDO, attribution, TemplateTypeEnum.DRIVER_CHECK_ORDER.getVal()));
            result.setDeviceAttribution(attribution.getName());
        } else {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "模式未实现");
        }
        return result;
    }

    @Override
    public boolean checkAllowWeighing(DeliveryQuery query) {
        String deviceSn = query.getDeviceSn();
        String deviceType = query.getDeviceType();
        Integer mode = query.getMode();
        String truckNo = query.getTruckNo();

        DeviceDO deviceDO = getDeviceDO(deviceSn, deviceType);
        DeviceBindingDO deviceBindingDO = getDeviceBindingDO(deviceDO);
        DeviceAttributionDO attribution = getDeviceAttributionDO(deviceBindingDO);

        LocalDateTime now = LocalDateTime.now();
        Integer sameTruckMinDuration;
        // 查询前一次同车牌号的称重时间
        WeighDataDO weighDataDO = weighDataService.lambdaQuery().eq(WeighDataDO::getAttributionId, attribution.getId()).eq(WeighDataDO::getTruckNo, truckNo)
                .orderByDesc(WeighDataDO::getWeighTime).last("limit 1").one();
        if (weighDataDO == null) {
            return true;
        }

        LocalDateTime weighTime = weighDataDO.getWeighTime();
        if (checkTruckNo(truckNo) && (Objects.equals(mode, DeviceModeEnum.TWO.getVal()) || Objects.equals(mode, DeviceModeEnum.FIVE.getVal()))) {
            // 查询设备配置
            sameTruckMinDuration = deviceBindingDO.getSameTruckMinDuration();
            if (sameTruckMinDuration == 0 || weighTime.plusMinutes(sameTruckMinDuration).isBefore(now)) {
                return true;
            } else {
                return false;
            }
        } else if (checkTruckNo(truckNo) && Objects.equals(mode, DeviceModeEnum.ONE.getVal())) {
            // 查询供应商配置
            DeliveryDO deliveryDO = getDeliveryDO(attribution, truckNo, 1);
            if (deliveryDO != null && deliveryDO.getStatus().equals(DeliveryStatusEnum.FIVE.value())) {
                // 查询确认单数据
                WeighDataConfirmDO confirmDO = getWeighDataConfirmDO(attribution, deviceSn, truckNo);

                if (confirmDO != null) {
                    String supplierId = deliveryDO.getSupplierExtId();
                    String createId = attribution.getCreateId();
                    SupplierConfigDO supplierConfigDO = supplierConfigService.lambdaQuery().eq(SupplierConfigDO::getSupplierExtId, supplierId).eq(SupplierConfigDO::getCreateId, createId)
                            .oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "外部供应商id未绑定"));
                    sameTruckMinDuration = supplierConfigDO.getSameTruckMinDuration();
                    if (sameTruckMinDuration == 0 || weighTime.plusMinutes(sameTruckMinDuration).isBefore(now)) {
                        return true;
                    } else {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    private boolean checkTruckNo(String truckNo) {
        if (StrUtil.isBlank(truckNo)) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "车牌号为空");
        }
        return true;
    }

    public WeighDataConfirmOriginExtDO getWeighDataConfirmOriginExtDO(WeighDataConfirmDO confirmDO, DeliveryDO deliveryDO, Integer timeout) {
        WeighDataConfirmOriginExtDO result = JSONUtil.toBean(confirmDO.getOriginJson(), WeighDataConfirmOriginExtDO.class);
        List<WeighDataExtDO> weighDataList = result.getTerminalConfirmData().getWeighDataList();
        if (CollUtil.isEmpty(weighDataList)) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "一二磅称重数据为空");
        }
        if (weighDataList.size() == 2) {
            result = new WeighDataConfirmOriginExtDO();
        } else if (weighDataList.size() == 1 && timeout != null) {
            if (timeout < 0) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "超时时间设置错误,不能小于0");
            }
            LocalDateTime gmtCreate = weighDataList.get(0).getGmtCreate();
            result.setTimeout(timeout != 0 && gmtCreate.plusMinutes(timeout).isBefore(LocalDateTime.now()));
            // 未超时更新运单状态为到场确认中
            if (!result.isTimeout()) {
                deliveryDO.setStatus(DeliveryStatusEnum.TWO.value());
                updateById(deliveryDO);
            }
        }
        return result;
    }

    private WeighDataConfirmDO getWeighDataConfirmDO(DeviceAttributionDO attribution, String deviceSn, String truckNo) {
        WeighDataConfirmDO confirmDO = weighDataConfirmService.lambdaQuery()
                .eq(WeighDataConfirmDO::getAttributionId, attribution.getId())
                .eq(WeighDataConfirmDO::getDeviceSn, deviceSn)
                .eq(WeighDataConfirmDO::getTruckNo, truckNo)
                .orderByDesc(WeighDataConfirmDO::getGmtCreate)
                .last("limit 1")
                .one();
        return confirmDO;
    }

    private DeliveryDO getDeliveryDO(DeviceAttributionDO attribution, String truckNo, Integer type) {
        DeliveryDO deliveryDO = lambdaQuery()
                .eq(DeliveryDO::getAttributionId, attribution.getId())
                .eq(DeliveryDO::getType, type)
                .eq(DeliveryDO::getTruckNo, truckNo)
                .notIn(DeliveryDO::getStatus, DeliveryStatusEnum.THREE.value(), DeliveryStatusEnum.FOUR.value())
                .orderByDesc(DeliveryDO::getGmtCreate)
                .last("limit 1")
                .one();
        return deliveryDO;
    }

    private @NotNull DeviceAttributionDO getDeviceAttributionDO(DeviceBindingDO deviceBindingDO) {
        DeviceAttributionDO attribution = deviceAttributionService.getById(deviceBindingDO.getAttributionId());
        if (attribution == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方信息不存在");
        }
        return attribution;
    }

    private DeviceBindingDO getDeviceBindingDO(DeviceDO deviceDO) {
        DeviceBindingDO deviceBindingDO = deviceBindingService.lambdaQuery()
                .eq(DeviceBindingDO::getDeviceId, deviceDO.getId())
                .oneOpt()
                .orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "设备未绑定归属方"));
        return deviceBindingDO;
    }

    private DeviceDO getDeviceDO(String deviceSn, String deviceType) {
        // 校验设备归属方
        DeviceDO deviceDO = deviceService.lambdaQuery()
                .eq(DeviceDO::getDeviceSn, deviceSn)
                .eq(StrUtil.isNotBlank(deviceType), DeviceDO::getDeviceType, deviceType)
                .oneOpt()
                .orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "设备不存在"));
        return deviceDO;
    }

    private List<PrintTemplateExtDO> queryPrintTemplateList(DeviceDO deviceDO, DeviceAttributionDO attribution, Byte templateType) {
        // 查询打印模版和次数
        List<PrintTemplateDO> templateList = printTemplateMapper.selectByDeviceIdAndStyle(
                deviceDO.getId(),
                attribution.getUid(),
                templateType,
                TemplateStyleEnum.ALL_IN_ONE.getVal(),
                null
        );

        if (CollUtil.isNotEmpty(templateList)) {
            List<PrintTemplateExtDO> templateExtDOList = templateList.stream().map(obj -> {
                PrintTemplateExtDO templateExtDO = new PrintTemplateExtDO();
                BeanUtils.copyProperties(obj, templateExtDO);
                templateExtDO.setContent(JSONUtil.parseObj(obj.getContent()));
                return templateExtDO;
            }).collect(Collectors.toList());
            return templateExtDOList;
        }
        return Collections.emptyList();
    }


    private String deliveryNoCheck(String no, DeliverySyncIdentityForm jsInfo, Long attributionId) {
        if (StrUtil.isBlank(no)) {
            return noUtil.generateUniqueId(1);
        } else {
            boolean signFlag = no.startsWith("1");
            boolean lengthFlag = no.length() == 14;
            DeliveryDO deliveryDO = this.lambdaQuery()
                    .eq(DeliveryDO::getNo, no)
                    .eq(BaseDO::getCreateId, jsInfo.getJsTenantId())
                    .eq(DeliveryDO::getAttributionId, attributionId)
                    .one();
            if (ObjUtil.isNotNull(deliveryDO)) {
                throw new BizErrorException(BizExceptionMessageEnum.ORDER_IS_HAS_SYNC, no, attributionId);
            }
            boolean repeatFlag = ObjUtil.isNull(deliveryDO);
            if (signFlag && lengthFlag && repeatFlag) {
                return no;
            } else {
                return noUtil.generateUniqueId(1);
            }
        }
    }
}
