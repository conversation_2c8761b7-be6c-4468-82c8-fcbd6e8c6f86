package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.pinming.material.v2.model.Page;
import cn.pinming.material.v2.model.PageList;
import cn.pinming.material.v2.model.QueryPage;
import cn.pinming.material.v2.model.WeighDataAssemble;
import cn.pinming.material.v2.model.dto.ReceiptRecycleDTO;
import cn.pinming.material.v2.model.dto.ReceiptRecyclePushDTO;
import cn.pinming.material.v2.model.form.WeighDataAssembleForm;
import cn.pinming.material.v2.model.query.RecycleQuery;
import cn.pinming.material.v2.model.vo.ReceiptRecycleDataVO;
import cn.pinming.material.v2.model.vo.ReceiptRecycleHeaderVO;
import cn.pinming.material.v2.model.vo.ReceiptRecyclePicVO;
import cn.pinming.microservice.contract.management.service.IMaterialContractService;
import cn.pinming.microservice.material.client.management.common.PageHeader;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataCheckDTO;
import cn.pinming.microservice.material.client.management.common.dto.ocr.SelfDeviceOcrDTO;
import cn.pinming.microservice.material.client.management.common.enums.*;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleExportForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleModuleForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleUpdateForm;
import cn.pinming.microservice.material.client.management.common.mapper.ReceiptRecycleMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.OCRModuleExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.ReceiptRecycleExtMapper;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.common.query.ReceiptRecycleQuery;
import cn.pinming.microservice.material.client.management.common.query.ReceiptRecycleResultQuery;
import cn.pinming.microservice.material.client.management.common.vo.*;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.AttributionCodeShowUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.CheckUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.client.management.service.biz.*;
import cn.pinming.microservice.material.client.management.service.business.CombineService;
import cn.pinming.microservice.material.client.management.service.business.ReceiptRecyclePushBusinessService;
import cn.pinming.microservice.material.client.management.service.business.RecycleService;
import cn.pinming.microservice.supplier.management.service.IMaterialSupplierService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 单据回收表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Service
@Slf4j
public class ReceiptRecycleServiceImpl extends ServiceImpl<ReceiptRecycleMapper, ReceiptRecycleDO> implements ReceiptRecycleService {

    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private FileOssService fileOssService;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private ReceiptRecycleModuleService receiptRecycleModuleService;
    @Resource
    private ReceiptRecycleWeighService receiptRecycleWeighService;
    @Resource
    private ReceiptModuleConfigService receiptModuleConfigService;
    @Resource
    private RecycleService recycleService;
    @Resource
    private OCRModuleExtMapper ocrModuleExtMapper;
    @Resource
    private ReceiptRecycleExtMapper receiptRecycleExtMapper;
    @Resource
    private CombineService combineService;
    @Resource
    private OCRModuleService ocrModuleService;
    @Resource
    private OcrModuleDetailService ocrModuleDetailService;
    @Resource
    private DeviceExtMapper deviceExtMapper;
    @Resource
    private DeveloperService developerService;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private ReceiptRecyclePushBusinessService receiptRecyclePushBusinessService;
    @Resource
    private AttributionCodeShowUtil attributionCodeShowUtil;
    @Resource
    private CheckUtil checkUtil;
    @Resource
    private IWeighDataConfirmService weighDataConfirmService;
    @Resource
    private WeighDataService weighDataService;
    @Resource
    private WeighDataPicService weighDataPicService;
    @Resource
    private UserService userService;
    @Resource
    private IReceiptRecycleBatchService receiptRecycleBatchService;
    @DubboReference
    private IMaterialContractService materialContractService;
    @DubboReference
    private IMaterialSupplierService materialSupplierService;

    @Override
    public IPage<ReceiptRecycleVO> pageList(ReceiptRecycleQuery query) {
        query.setUid(userIdUtil.getUId());
        if (query.getRecycleTimeEnd() != null) {
            query.setRecycleTimeEnd(query.getRecycleTimeEnd().plusDays(1));
        }
        IPage<ReceiptRecycleVO> page = receiptRecycleExtMapper.pageList(query);
        if (CollUtil.isEmpty(page.getRecords())) {
            return page;
        }
        //组装结果
        return page.convert(receiptRecycleVO -> {
            if (StrUtil.isNotEmpty(receiptRecycleVO.getRecyclePic())) {
                receiptRecycleVO.setRecyclePicUrls(fileOssService.getUrlByUuids(Arrays.asList(receiptRecycleVO.getRecyclePic().split(","))));
            }
            if (StrUtil.isNotEmpty(receiptRecycleVO.getReceiptFailTypes())) {
                receiptRecycleVO.setReceiptFailTypeDescList(ReceiptFailTypeEnum.getDescList(receiptRecycleVO.getReceiptFailTypes()));
            }
            String code = attributionCodeShowUtil.choose(receiptRecycleVO.getAttributionId(), receiptRecycleVO.getAttributionCode(), query.getAttributionCode());
            receiptRecycleVO.setAttributionCode(code);
            return receiptRecycleVO;
        });
    }

    @Override
    public ReceiptRecycleDetailsVO details(Long id) {
        ReceiptRecycleDO receiptRecycleDO = this.getById(id);
        if (receiptRecycleDO == null) {
            return null;
        }
        //单据回收
        ReceiptRecycleDetailsVO receiptRecycleDetailsVO = new ReceiptRecycleDetailsVO();
        BeanUtil.copyProperties(receiptRecycleDO, receiptRecycleDetailsVO);
        if (StrUtil.isNotEmpty(receiptRecycleDO.getRecyclePic())) {
            receiptRecycleDetailsVO.setRecyclePicUrls(fileOssService.getUrlByUuids(Arrays.asList(receiptRecycleDO.getRecyclePic().split(","))));
        }
        //获取归属方信息
        DeviceAttributionDO deviceAttributionDO = deviceAttributionService.getById(receiptRecycleDO.getAttributionId());
        if (deviceAttributionDO != null) {
            receiptRecycleDetailsVO.setAttributionName(deviceAttributionDO.getName());
            receiptRecycleDetailsVO.setAttributionCode(deviceAttributionDO.getCode());
        }
        if (receiptRecycleDO.getOcrType() != null && receiptRecycleDO.getOcrType().equals(OcrTypeEnum.AUTO_WEIGH.getValue())) {
            //称重数据组装
            List<ReceiptRecycleWeighDO> receiptRecycleWeighDOList = receiptRecycleWeighService.lambdaQuery()
                    .eq(ReceiptRecycleWeighDO::getReceiptRecycleId, receiptRecycleDO.getId())
                    .eq(Objects.equals(receiptRecycleDO.getRecycleStatus(), RecycleStatusEnum.SUCCESS.getValue()), ReceiptRecycleWeighDO::getIsEffective, IsEffectiveEnum.YES.getValue())
                    .orderByDesc(ReceiptRecycleWeighDO::getWeight).list();
            Integer weighStatus = RecycleStatusEnum.FAIL.getValue();
            if (CollUtil.isNotEmpty(receiptRecycleWeighDOList)) {
                weighStatus = receiptRecycleWeighDOList.stream().filter(receiptRecycleWeighDO -> receiptRecycleWeighDO.getIsEffective().equals(IsEffectiveEnum.YES.getValue()))
                        .count() == 2 ? RecycleStatusEnum.SUCCESS.getValue() : RecycleStatusEnum.FAIL.getValue();
                receiptRecycleDetailsVO.setWeighList(receiptRecycleWeighDOList.stream().map(receiptRecycleWeighDO -> {
                    ReceiptRecycleWeighVO receiptRecycleWeighVO = new ReceiptRecycleWeighVO();
                    BeanUtil.copyProperties(receiptRecycleWeighDO, receiptRecycleWeighVO);
                    if (StrUtil.isNotEmpty(receiptRecycleWeighDO.getFileIds())) {
                        receiptRecycleWeighVO.setFileIdUrls(fileOssService.getUrlByUuids(Arrays.asList(receiptRecycleWeighDO.getFileIds().split(","))));
                    }
                    return receiptRecycleWeighVO;
                }).collect(Collectors.toList()));
            }
            receiptRecycleDetailsVO.setWeighStatus(weighStatus);
        }
        //模板数据组装
        List<ReceiptRecycleModuleVO> receiptRecycleModuleVOList = receiptRecycleModuleService.queryReceiptRecycleModuleVO(receiptRecycleDO);
        Integer moduleStatus = RecycleStatusEnum.FAIL.getValue();
        if (CollUtil.isNotEmpty(receiptRecycleModuleVOList)) {
            receiptRecycleDetailsVO.setModuleList(receiptRecycleModuleVOList);
            moduleStatus = receiptRecycleModuleVOList.stream().anyMatch(receiptRecycleModuleDO ->
                    receiptRecycleModuleDO.getIsEffective().equals(IsEffectiveEnum.NO.getValue())) ? RecycleStatusEnum.FAIL.getValue() : RecycleStatusEnum.SUCCESS.getValue();
        }
        receiptRecycleDetailsVO.setModuleStatus(moduleStatus);
        return receiptRecycleDetailsVO;
    }

    @Override
    public IPage<ReceiptRecycleResultVO> resultList(ReceiptRecycleResultQuery query) {
        //获取模板列名
        List<PageHeader.Column> headers = ocrModuleExtMapper.recycleModuleDetailShow(query.getModuleId(), (byte) 1)
                .stream().filter(om -> om.getRecycleDetailType() != null && om.getRecycleDetailType() == (byte) 1).map(vo -> new PageHeader.Column(vo.getKeyName(), vo.getModuleDetailId().toString())).collect(Collectors.toList());
        Integer ocrType = receiptModuleConfigService.getOcrType(query.getModuleId());
        //获取数据
        query.setUid(userIdUtil.getUId());
        PageHeader<ReceiptRecycleResultVO> page = receiptRecycleExtMapper.resultList(new PageHeader<>(query.getCurrent(), query.getSize(), headers, ocrType), query);
        if (CollUtil.isEmpty(page.getRecords())) {
            return new PageHeader<>(page.getCurrent(), page.getSize(), page.getTotal(), headers, ocrType);
        }
        page.setTotal(query.getTotal());
        List<Long> receiptRecycleIdList = page.getRecords().stream().map(ReceiptRecycleResultVO::getId).distinct().collect(Collectors.toList());
        //获取模板数据
        Set<Long> moduleDetailIdSet = headers.stream().map(p -> Long.parseLong(p.getColumnKey())).collect(Collectors.toSet());
        List<ReceiptRecycleModuleDO> receiptRecycleModuleDOList = receiptRecycleModuleService.lambdaQuery()
                .in(ReceiptRecycleModuleDO::getReceiptRecycleId, receiptRecycleIdList)
                .in(CollUtil.isNotEmpty(moduleDetailIdSet), ReceiptRecycleModuleDO::getOcrModuleDetailId, moduleDetailIdSet)
                .eq(ReceiptRecycleModuleDO::getIsEffective, IsEffectiveEnum.YES.getValue())
                .list();
        Map<Long, List<ReceiptRecycleModuleDO>> receiptRecycleModuleDOMap = new HashMap<>(receiptRecycleModuleDOList.size());
        if (CollUtil.isNotEmpty(receiptRecycleModuleDOList)) {
            receiptRecycleModuleDOMap.putAll(receiptRecycleModuleDOList.stream().collect(Collectors.groupingBy(ReceiptRecycleModuleDO::getReceiptRecycleId)));
        }
        //组装结果
        return page.convert(receiptRecycleResultVO -> {
            if (receiptRecycleModuleDOMap.containsKey(receiptRecycleResultVO.getId())) {
                receiptRecycleResultVO.setModuleMap(receiptRecycleModuleDOMap.get(receiptRecycleResultVO.getId())
                        .stream().collect(Collectors.toMap(ReceiptRecycleModuleDO::getOcrModuleDetailId, d -> d.getKeyValue() == null ? "" : d.getKeyValue())));
            }
            return receiptRecycleResultVO;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cameraAdd(ReceiptRecycleForm form) {
        Long batchId = form.getBatchId();
        String uId = userIdUtil.getUId();
        DeviceAttributionDO deviceAttributionDO = deviceAttributionService.getById(form.getAttributionId());
        if (deviceAttributionDO == null || !deviceAttributionDO.getUid().equals(uId)) {
            throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_IS_ERROR);
        }
        DeveloperDO developerDO = developerService.lambdaQuery().eq(DeveloperDO::getCreateId, uId)
                .eq(DeveloperDO::getAppId, DeveloperAppEnum.RECYCLE.value()).one();
        if (ObjectUtil.isNull(developerDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.SIGN_UP_APP);
        }
        if (developerDO.getType().equals(DeveloperTypeEnum.STOP.value())) {
            throw new BizErrorException(BizExceptionMessageEnum.SERVICE_DISABLED, DeveloperAppEnum.RECYCLE.description());
        }

        if (batchId != null) {
            ReceiptRecycleBatchDO batchDO = receiptRecycleBatchService.getById(batchId);
            if (batchDO == null) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "该回收批次不存在");
            }
            if (batchDO.getStatus() == 1) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "该回收批次已归档");
            }
        }

        //保存回收记录
        ReceiptRecycleDO receiptRecycleDO = new ReceiptRecycleDO();
        receiptRecycleDO.setUid(uId);
        receiptRecycleDO.setRecycleSource(RecycleSourceEnum.CAMERA.getValue());
        receiptRecycleDO.setRecycleStatus(RecycleStatusEnum.WAIT.getValue());
        receiptRecycleDO.setAttributionId(form.getAttributionId());
        receiptRecycleDO.setBatchId(form.getBatchId());
        if (CollUtil.isNotEmpty(form.getRecyclePics())) {
            fileOssService.confirmList(form.getRecyclePics());
            receiptRecycleDO.setRecyclePic(String.join(",", form.getRecyclePics()));
        }
        this.save(receiptRecycleDO);
        //异步处理回收
        recycleService.syncProcessRecycle(receiptRecycleDO);
    }

    @Override
    public void terminalRecycle(String deviceSn, MultipartFile[] files) {
        WeighDataCheckDTO dto = deviceExtMapper.selectInfoByDeviceSn(deviceSn, DeveloperAppEnum.RECYCLE.value(), DeviceTypeEnum.RECEIPT_RECYCLE.name());
        // 设备是否准入
        if (ObjectUtil.isNull(dto)) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_NOT_EXIST);
        }
        // 设备是否启用
        if (dto.getIsUsed().equals(DeviceIsUsedEnum.STOP.value())) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_USED);
        }
        // 设备是否占有
        if (StrUtil.isBlank(dto.getUid())) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_HAS_NO_OCCUPIED);
        }
        // 设备是否绑定
        if (dto.getAttributionId() == null) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_NOT_HAVE_ATTRIBUTION);
        }
        // 设备是否接收
        if (dto.getReceive().equals(DeviceReceiveEnum.STOP.value())) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_NOT_RECEIVE);
        }
        // 服务是否启用
        if (dto.getType().equals(DeveloperTypeEnum.STOP.value())) {
            throw new BizErrorException(BizExceptionMessageEnum.SERVICE_DISABLED, DeveloperAppEnum.RECYCLE.description());
        }

        //3.1 需通过设备SN判断设备是否绑定了批次，未绑定批次时，按原逻辑，回收任务数据不关联批次；绑定时，按3.2处理；
        //3.2 需通过设备SN判断设备绑定的批次所属归属方与该设备当前绑定的归属方是否相同，不同时，拒收照片，相同时，关联该任务数据关联回收批次；
        ReceiptRecycleBatchDO batchDO = receiptRecycleBatchService.checkDeviceId(dto.getDeviceId(), dto.getUid());
        Long batchId = null;
        if (ObjectUtil.isNotNull(batchDO)) {
            if (batchDO.getStatus() == 1) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "该回收批次已归档");
            }
            if (!batchDO.getAttributionId().equals(dto.getAttributionId())) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "该设备绑定的回收批次归属方与当前归属方不一致");
            }
            batchId = batchDO.getId();
        }

        //上传图片
        List<String> recyclePics;
        try {
            recyclePics = Arrays.stream(files).map(recyclePic -> fileServiceProxy.uploadFileForUuid(recyclePic)).collect(Collectors.toList());
        } catch (Exception e) {
            throw new BizErrorException(BizExceptionMessageEnum.RECYCLE_UPLOAD_FILE_ERROR);
        }
        //保存回收记录
        ReceiptRecycleDO receiptRecycleDO = new ReceiptRecycleDO();
        receiptRecycleDO.setUid(dto.getUid());
        receiptRecycleDO.setRecycleSource(RecycleSourceEnum.DEVICE.getValue());
        receiptRecycleDO.setRecycleStatus(RecycleStatusEnum.WAIT.getValue());
        receiptRecycleDO.setAttributionId(dto.getAttributionId());
        receiptRecycleDO.setDeviceSn(deviceSn);
        receiptRecycleDO.setAuxiliaryCode(dto.getAuxiliaryCode());
        receiptRecycleDO.setBatchId(batchId);
        if (CollUtil.isNotEmpty(recyclePics)) {
            receiptRecycleDO.setRecyclePic(String.join(",", recyclePics));
        }
        this.save(receiptRecycleDO);
        //异步处理回收
        recycleService.syncProcessRecycle(receiptRecycleDO);
    }

    @Override
    public DeviceAttributionDO history() {
        ReceiptRecycleDO receiptRecycleDO = this.lambdaQuery().eq(ReceiptRecycleDO::getUid, userIdUtil.getUId()).isNotNull(ReceiptRecycleDO::getAttributionId).orderByDesc(ReceiptRecycleDO::getGmtCreate).last("limit 1").one();
        if (receiptRecycleDO != null) {
            return deviceAttributionService.getById(receiptRecycleDO.getAttributionId());
        }
        return null;
    }

    @Override
    public WeighDataAssemble assembleWeigh(WeighDataAssembleForm assembleForm) {
        checkUtil.appCheck(userIdUtil.getUId(), DeveloperAppEnum.ASSEMBLE);
        return combineService.weighDataAssemble(Arrays.asList(assembleForm.getFirst(), assembleForm.getSecond()), assembleForm.getAttributionCode(), userIdUtil.getUId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void correctionWeigh(ReceiptRecycleUpdateForm form) {
        ReceiptRecycleDO receiptRecycleDO = this.getById(form.getId());
        //更新完整称重数据到回收记录中
        receiptRecycleWeighService.updateCompleteWeigh(form.getRecordIdList(), receiptRecycleDO);
        //确认组装
        combineService.confirmAssemble(form.getRecordIdList(), null, receiptRecycleDO.getAttributionId(), receiptRecycleDO.getUid(), null);

        List<ReceiptRecycleWeighDO> receiptRecycleWeighDOList = receiptRecycleWeighService.lambdaQuery().eq(ReceiptRecycleWeighDO::getReceiptRecycleId, receiptRecycleDO.getId())
                .eq(ReceiptRecycleWeighDO::getIsEffective, IsEffectiveEnum.YES.getValue()).orderByDesc(ReceiptRecycleWeighDO::getWeight).list();
        if (receiptRecycleWeighDOList.size() != 2) {
            throw new BizErrorException(BizExceptionMessageEnum.RECYCLE_WEIGH_DATA_ERROR);
        }
        //回收称重数据修改
        BeanUtils.copyProperties(form, receiptRecycleDO);
        //转换为吨单位
        receiptRecycleDO.setWeightGross(WeighDataUnitEnum.convertTon(form.getWeightGross(), form.getUnit()));
        receiptRecycleDO.setWeightTare(WeighDataUnitEnum.convertTon(form.getWeightTare(), form.getUnit()));
        receiptRecycleDO.setWeightNet(WeighDataUnitEnum.convertTon(form.getWeightNet(), form.getUnit()));
        receiptRecycleDO.setUnit(WeighDataUnitEnum.TON.value());
        this.updateById(receiptRecycleDO);
    }

    @Override
    public void assembleModule(Long id) {
        ReceiptRecycleDO receiptRecycleDO = this.getById(id);
        recycleService.assembleModule(receiptRecycleDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void correctionModule(ReceiptRecycleUpdateForm form) {
        ReceiptRecycleDO receiptRecycleDO = this.getById(form.getId());
        if (receiptRecycleDO.getModuleId() == null && form.getModuleId() == null) {
            throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_EMPTY);
        }
        //更新模板数据
        if (CollUtil.isNotEmpty(form.getModuleFormList())) {
            Map<Long, String> moduleValueMap = form.getModuleFormList().stream().filter(f -> f.getKeyValue() != null).collect(Collectors.toMap(ReceiptRecycleModuleForm::getId, ReceiptRecycleModuleForm::getKeyValue));
            List<ReceiptRecycleModuleDO> receiptRecycleModuleDOList = receiptRecycleModuleService.lambdaQuery().in(ReceiptRecycleModuleDO::getId, moduleValueMap.keySet())
                    .eq(ReceiptRecycleModuleDO::getReceiptRecycleId, receiptRecycleDO.getId()).list();
            if (CollUtil.isEmpty(receiptRecycleModuleDOList)) {
                throw new BizErrorException(BizExceptionMessageEnum.FORM_DATA_NOT_FOUND);
            }
            receiptRecycleModuleDOList.forEach(receiptRecycleModuleDO -> {
                String value = moduleValueMap.get(receiptRecycleModuleDO.getId());
                if (!OcrKeyTypeEnum.validate(receiptRecycleModuleDO.getKeyType(), value)) {
                    throw new BizErrorException(BizExceptionMessageEnum.OCR_MODULE_KEY_TYPE_MISMATCH);
                }
                receiptRecycleModuleDO.setKeyValue(value);
            });
            receiptRecycleModuleService.updateBatchById(receiptRecycleModuleDOList);
        }
        if (receiptRecycleDO.getModuleId() == null) {
            recycleService.ocrModuleMatch(form.getModuleId(), receiptRecycleDO);
        } else {
            receiptRecycleModuleService.verifyAndCorrectionModule(receiptRecycleDO, receiptRecycleDO.getModuleId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void chooseModule(ReceiptRecycleUpdateForm form) {
        ReceiptRecycleDO receiptRecycleDO = this.getById(form.getId());
        if (receiptRecycleDO.getModuleId() != null || form.getModuleId() == null) {
            throw new BizErrorException(BizExceptionMessageEnum.RECYCLE_CHOOSE_MODULE_EXIST);
        }
        recycleService.saveChooseModule(form.getModuleId(), receiptRecycleDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void successRecycle(Long id) {
        ReceiptRecycleDO receiptRecycleDO = this.getById(id);
        if (receiptRecycleDO.getOcrType() == null || receiptRecycleDO.getModuleId() == null) {
            throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_EMPTY);
        }
        if (receiptRecycleDO.getRecycleStatus().equals(RecycleStatusEnum.SUCCESS.getValue())) {
            return;
        }
        if (receiptRecycleDO.getOcrType().equals(OcrTypeEnum.AUTO_WEIGH.getValue())) {
            //获取称重数据
            Integer weighCount = receiptRecycleWeighService.lambdaQuery()
                    .eq(ReceiptRecycleWeighDO::getReceiptRecycleId, receiptRecycleDO.getId())
                    .eq(ReceiptRecycleWeighDO::getIsEffective, IsEffectiveEnum.YES.getValue())
                    .count();
            if (weighCount != 2) {
                throw new BizErrorException(BizExceptionMessageEnum.RECYCLE_WEIGH_DATA_ERROR);
            }
        }
        //获取模板数据
        Integer moduleCount = receiptRecycleModuleService.lambdaQuery()
                .eq(ReceiptRecycleModuleDO::getReceiptRecycleId, receiptRecycleDO.getId())
                .eq(ReceiptRecycleModuleDO::getModuleId, receiptRecycleDO.getModuleId())
                .eq(ReceiptRecycleModuleDO::getIsEffective, IsEffectiveEnum.NO.getValue())
                .count();
        if (moduleCount > 0) {
            throw new BizErrorException(BizExceptionMessageEnum.RECYCLE_MODULE_DATA_ERROR);
        }
        Integer preStatus = receiptRecycleDO.getRecycleStatus();//修改前状态
        receiptRecycleDO.setRecycleStatus(RecycleStatusEnum.SUCCESS.getValue());
        receiptRecycleDO.setRecycleTime(LocalDateTime.now());
        if (!this.lambdaUpdate().eq(ReceiptRecycleDO::getId, id)
                .eq(ReceiptRecycleDO::getRecycleStatus, preStatus)
                .update(receiptRecycleDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.RECYCLE_SUCCESS_OPERATE_ERROR);
        }
        //异步推送数据
        receiptRecyclePushBusinessService.syncPushReceiptRecycle(receiptRecycleDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invalidRecycle(Long id) {
        ReceiptRecycleDO receiptRecycleDO = this.getById(id);
        if (receiptRecycleDO.getRecycleStatus().equals(RecycleStatusEnum.SUCCESS.getValue())) {
            throw new BizErrorException(BizExceptionMessageEnum.RECYCLE_STATUS_SUCCESS_ERROR);
        }
        if (receiptRecycleDO.getRecycleStatus().equals(RecycleStatusEnum.INVALID.getValue())) {
            return;
        }
        Integer preStatus = receiptRecycleDO.getRecycleStatus();//修改前状态
        receiptRecycleDO.setRecycleStatus(RecycleStatusEnum.INVALID.getValue());
        if (!this.lambdaUpdate().eq(ReceiptRecycleDO::getId, id)
                .eq(ReceiptRecycleDO::getRecycleStatus, preStatus)
                .update(receiptRecycleDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.RECYCLE_INVALID_OPERATE_ERROR);
        }
    }

    @Override
    public void resultExport(HttpServletResponse response, ReceiptRecycleExportForm form) {
        ServletOutputStream out = null;
        ExcelWriter writer = ExcelUtil.getWriter(true);
        try {
            form.setUid(userIdUtil.getUId());
            //固定表头
            Map<String, String> fixedHeader = new HashMap<>();
            fixedHeader.put("truckNo", "车牌号");
            fixedHeader.put("weightGross", "毛重");
            fixedHeader.put("weightTare", "皮重");
            fixedHeader.put("weightNet", "净重");
            fixedHeader.put("weightGrossTime", "毛重时间");
            fixedHeader.put("weightTareTime", "皮重时间");
            fixedHeader.put("weighType", "过磅类型");

            boolean isFirst = true;
            for (Map.Entry<String, List<Long>> entry : form.getModuleList().entrySet()) {
                //写入sheet名称
                OcrModuleDO ocrModuleDO = ocrModuleService.getById(Long.valueOf(entry.getKey()));
                String sheetName = ocrModuleDO != null ? ocrModuleDO.getName() : entry.getKey() + "回收匹配结果";
                if (isFirst) {
                    writer.renameSheet(sheetName);
                    isFirst = false;
                } else {
                    writer.setSheet(sheetName);
                }
                //写入标题
                List<String> headerList = new ArrayList<>();
                form.getFieldList().forEach(field -> headerList.add(fixedHeader.getOrDefault(field, field)));
                ocrModuleDetailService.listByIds(entry.getValue()).forEach(moduleDO -> headerList.add(moduleDO.getKeyName()));
                headerList.add("归属方名称");
                headerList.add("归属方code");
                writer.writeHeadRow(headerList);
                //写入数据
                writerData(form, writer, entry);
            }
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            String fileName = URLEncoder.encode(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + "单据匹配结果导出", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {
            log.error("单据回收匹配结果导出失败", e);
        } finally {
            if (out != null) IoUtil.close(out);
            writer.close();
        }
    }

    @SneakyThrows
    @Override
    public void selfDeviceRecycle(SelfDeviceOcrDTO ocrDTO) {
        String deviceSn = ocrDTO.getDeviceSn();
        WeighDataCheckDTO dto = deviceExtMapper.selectInfoByDeviceSn(deviceSn, DeveloperAppEnum.RECYCLE.value(), DeviceTypeEnum.RECEIPT_RECYCLE.name());
        // 设备是否准入
        if (ObjectUtil.isNull(dto)) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_NOT_EXIST);
        }
        // 设备是否启用
        if (dto.getIsUsed().equals(DeviceIsUsedEnum.STOP.value())) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_USED);
        }
        // 设备是否占有
        if (StrUtil.isBlank(dto.getUid())) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_HAS_NO_OCCUPIED);
        }
        // 设备是否绑定
        if (dto.getAttributionId() == null) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_NOT_HAVE_ATTRIBUTION);
        }
        // 设备是否接收
        if (dto.getReceive().equals(DeviceReceiveEnum.STOP.value())) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_NOT_RECEIVE);
        }
        // 服务是否启用
        if (dto.getType().equals(DeveloperTypeEnum.STOP.value())) {
            throw new BizErrorException(BizExceptionMessageEnum.SERVICE_DISABLED, DeveloperAppEnum.RECYCLE.description());
        }

        //保存回收记录
        ReceiptRecycleDO receiptRecycleDO = new ReceiptRecycleDO();
        BeanUtil.copyProperties(ocrDTO, receiptRecycleDO);
        receiptRecycleDO.setUid(dto.getUid());
        receiptRecycleDO.setRecycleSource(RecycleSourceEnum.DEVICE.getValue());
        receiptRecycleDO.setRecycleStatus(RecycleStatusEnum.WAIT.getValue());
        receiptRecycleDO.setAttributionId(dto.getAttributionId());
        receiptRecycleDO.setDeviceSn(deviceSn);
        receiptRecycleDO.setRecycleSource(4);
        receiptRecycleDO.setAuxiliaryCode(dto.getAuxiliaryCode());
        //上传图片
        List<String> recyclePics = ocrDTO.getRecyclePics();
        if (CollUtil.isNotEmpty(recyclePics)) {
            receiptRecycleDO.setRecyclePic(String.join(",", recyclePics));
        }
        this.save(receiptRecycleDO);

        Long id = receiptRecycleDO.getId();
        saveReceiptRecycleWeigh(id, ocrDTO.getLocalId(), deviceSn, dto.getUid());
        //异步处理回收
        recycleService.syncProcessRecycle(receiptRecycleDO);
    }

    @Override
    public PageList<ReceiptRecycleDTO> recycleSimpleList(String appKeyHeader, QueryPage<RecycleQuery> query) {
        UserDO userDO = userService.getUserByAppKey(appKeyHeader);
        String uid = userDO.getUid();
        checkUtil.appCheck(uid, DeveloperAppEnum.QUERY);

        Page page = query.getPage();
        Integer current = page.getCurrent();
        int size = page.getSize() > 100 ? 100 : page.getSize();
        PageList<ReceiptRecycleDTO> result = new PageList<>();
        result.setCurrent(current);
        result.setSize(size);

        RecycleQuery recycleQuery = query.getT();
        Long batchId = recycleQuery.getBatchId();
        String attentionCode = recycleQuery.getAttentionCode();

        ReceiptRecycleQuery receiptRecycleQuery = new ReceiptRecycleQuery();
        receiptRecycleQuery.setUid(uid);
        receiptRecycleQuery.setBatchId(batchId);
        receiptRecycleQuery.setAttributionCode(attentionCode);
        receiptRecycleQuery.setCurrent(current);
        receiptRecycleQuery.setSize(size);

        IPage<ReceiptRecycleVO> pageList = receiptRecycleExtMapper.pageList(receiptRecycleQuery);
        if (CollUtil.isNotEmpty(pageList.getRecords())) {
            result.setTotal((int) pageList.getTotal());
            result.setPages((int) pageList.getPages());
            result.setDataList(BeanUtil.copyToList(pageList.getRecords(), ReceiptRecycleDTO.class));
        }
        return result;
    }

    @Override
    public ReceiptRecyclePushDTO recycleDetail(String appKeyHeader, Long id) {
        UserDO userDO = userService.getUserByAppKey(appKeyHeader);
        String uid = userDO.getUid();
        checkUtil.appCheck(uid, DeveloperAppEnum.QUERY);
        // 查询回收记录
        ReceiptRecycleDO receiptRecycleDO = getById(id);
        if (receiptRecycleDO == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "回收记录不存在");
        }
        if (!receiptRecycleDO.getUid().equals(uid)) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "非本租户回收记录");
        }
        // 模板数据组装
        List<ReceiptRecycleModuleDO> receiptRecycleModuleDOList = receiptRecycleModuleService.lambdaQuery()
                .eq(ReceiptRecycleModuleDO::getReceiptRecycleId, receiptRecycleDO.getId())
                .eq(ReceiptRecycleModuleDO::getModuleId, receiptRecycleDO.getModuleId())
                .eq(ReceiptRecycleModuleDO::getIsEffective, IsEffectiveEnum.YES.getValue())
                .list();
        List<ReceiptRecycleWeighDO> receiptRecycleWeighDOList = null;
        if (receiptRecycleDO.getOcrType() != null && receiptRecycleDO.getOcrType().equals(OcrTypeEnum.AUTO_WEIGH.getValue())) {
            // 称重数据组装
            receiptRecycleWeighDOList = receiptRecycleWeighService.lambdaQuery()
                    .eq(ReceiptRecycleWeighDO::getReceiptRecycleId, receiptRecycleDO.getId())
                    .eq(ReceiptRecycleWeighDO::getIsEffective, IsEffectiveEnum.YES.getValue())
                    .orderByDesc(ReceiptRecycleWeighDO::getWeight).list();
        }

        return receiptRecyclePushBusinessService.buildReceiptRecyclePushDTO(receiptRecycleDO, receiptRecycleModuleDOList, receiptRecycleWeighDOList);
    }

    @Override
    public ReceiptRecycleDataVO ocrRecycleList(String uid, cn.pinming.material.v2.model.query.ReceiptRecycleQuery query) {
        List<ReceiptModuleDetailConfigVO> headerList = ocrModuleExtMapper.recycleModuleDetailShow(query.getModuleId(), (byte) 1);
        ReceiptRecycleDataVO result = new ReceiptRecycleDataVO();

        Set<Long> moduleDetailIdSet = new HashSet<>();
        if (CollUtil.isNotEmpty(headerList)) {
            List<ReceiptRecycleHeaderVO> headers = headerList.stream().filter(om -> om.getRecycleDetailType() != null && om.getRecycleDetailType() == (byte) 1)
                    .map(vo -> {
                        ReceiptRecycleHeaderVO headerVO = new ReceiptRecycleHeaderVO();
                        headerVO.setColumnKey(vo.getModuleDetailId().toString());
                        headerVO.setColumnName(vo.getKeyName());
                        moduleDetailIdSet.add(vo.getModuleDetailId());
                        return headerVO;
                    }).collect(Collectors.toList());
            result.setHeader(headers);
        }

        List<cn.pinming.material.v2.model.vo.ReceiptRecycleVO> recycleList = receiptRecycleExtMapper.ocrRecycleList(query, uid);
        if (CollUtil.isNotEmpty(recycleList)) {
            long count = recycleList.stream().filter(o -> o.getPushStatus() != 3).count();
            result.setUnPushCount((int) count);
            List<cn.pinming.material.v2.model.vo.ReceiptRecycleVO> pushList = recycleList.stream().filter(o -> o.getPushStatus() == 3).collect(Collectors.toList());
            List<Long> receiptRecycleIdList = pushList.stream().map(cn.pinming.material.v2.model.vo.ReceiptRecycleVO::getId).distinct().collect(Collectors.toList());
            List<ReceiptRecycleModuleDO> receiptRecycleModuleDOList = receiptRecycleModuleService.lambdaQuery()
                    .in(ReceiptRecycleModuleDO::getReceiptRecycleId, receiptRecycleIdList)
                    .in(CollUtil.isNotEmpty(moduleDetailIdSet), ReceiptRecycleModuleDO::getOcrModuleDetailId, moduleDetailIdSet)
                    .eq(ReceiptRecycleModuleDO::getIsEffective, IsEffectiveEnum.YES.getValue())
                    .list();

            Map<Long, List<ReceiptRecycleModuleDO>> receiptRecycleModuleDOMap = new HashMap<>(receiptRecycleModuleDOList.size());
            if (CollUtil.isNotEmpty(receiptRecycleModuleDOList)) {
                receiptRecycleModuleDOMap.putAll(receiptRecycleModuleDOList.stream().collect(Collectors.groupingBy(ReceiptRecycleModuleDO::getReceiptRecycleId)));
            }

            pushList.forEach(o -> {
                if (receiptRecycleModuleDOMap.containsKey(o.getId())) {
                    Map<Long, String> collect = receiptRecycleModuleDOMap.get(o.getId())
                            .stream().collect(Collectors.toMap(ReceiptRecycleModuleDO::getOcrModuleDetailId, d -> d.getKeyValue() == null ? "" : d.getKeyValue()));
                    o.setModuleMap(collect);
                }

                String uuid = o.getUuid();
                if (StrUtil.isNotBlank(uuid)) {
                    List<String> uuidList = StrUtil.split(uuid, StrUtil.COMMA);
                    o.setPicList(fileOssService.getUrlByUuids(uuidList));
                }

            });
            result.setList(pushList);
        }
        return result;
    }

    @Override
    public ReceiptRecyclePicVO ocrRecyclePic(Long recycleId) {
        ReceiptRecycleDO recycleDO = getById(recycleId);
        ReceiptRecyclePicVO result = new ReceiptRecyclePicVO();
        if (recycleDO != null) {
            String recyclePic = recycleDO.getRecyclePic();
            if (StrUtil.isNotBlank(recyclePic)) {
                List<String> fileIdList = StrUtil.split(recyclePic, StrUtil.COMMA);
                result.setUrls(fileOssService.getUrlByUuids(fileIdList));
            }
        }
        return result;
    }

    public void saveReceiptRecycleWeigh(Long id, String localId, String deviceSn, String uid) {
        WeighDataConfirmDO confirmDO = weighDataConfirmService.lambdaQuery().eq(WeighDataConfirmDO::getLocalId, localId).one();
        List<String> recordIdList = new ArrayList<>();
        if (confirmDO != null) {
            recordIdList.add(confirmDO.getRecordId1());
            recordIdList.add(confirmDO.getRecordId2());
        }
        Map<String, List<String>> dataPicUuidMap = weighDataPicService.getUuid(recordIdList);
        recordIdList.forEach(recordId -> {
            WeighDataDO dataDO = weighDataService.lambdaQuery().eq(WeighDataDO::getRecordId, recordId).one();
            ReceiptRecycleWeighDO receiptRecycleWeighDO = new ReceiptRecycleWeighDO();
            receiptRecycleWeighDO.setUid(uid);
            receiptRecycleWeighDO.setReceiptRecycleId(id);
            receiptRecycleWeighDO.setRecordId(recordId);
            receiptRecycleWeighDO.setWeighDataId(dataDO.getId());
            receiptRecycleWeighDO.setDeviceSn(deviceSn);
            receiptRecycleWeighDO.setWeight(dataDO.getWeight());
            receiptRecycleWeighDO.setUnit(dataDO.getUnit());
            receiptRecycleWeighDO.setWeighTime(dataDO.getWeighTime());
            receiptRecycleWeighDO.setRiskGrade(dataDO.getRiskGrade());
            receiptRecycleWeighDO.setIsEffective(IsEffectiveEnum.YES.getValue());
            if (dataPicUuidMap.containsKey(recordId)) {
                receiptRecycleWeighDO.setFileIds(StrUtil.join(StrUtil.COMMA, dataPicUuidMap.get(recordId)));
            }
            receiptRecycleWeighService.save(receiptRecycleWeighDO);
        });
    }

    private void writerData(ReceiptRecycleExportForm form, ExcelWriter writer, Map.Entry<String, List<Long>> entry) {
        form.setModuleId(Long.valueOf(entry.getKey()));
        List<Map<String, Object>> recycleResultMapList = receiptRecycleExtMapper.resultExport(form);
        if (CollUtil.isEmpty(recycleResultMapList)) {
            writer.write(new ArrayList<>(), true);
            return;
        }
        List<Long> receiptRecycleIdList = recycleResultMapList.stream().map(mp -> Long.valueOf(mp.get("id").toString())).collect(Collectors.toList());
//        Map<Long, Map<Long, String>> moduleValueMap = receiptRecycleModuleService.lambdaQuery()
//                .in(ReceiptRecycleModuleDO::getReceiptRecycleId, receiptRecycleIdList)
//                .in(CollUtil.isNotEmpty(entry.getValue()), ReceiptRecycleModuleDO::getOcrModuleDetailId, entry.getValue())
//                .eq(ReceiptRecycleModuleDO::getIsEffective, IsEffectiveEnum.YES.getValue())
//                .list().stream().collect(Collectors.groupingBy(ReceiptRecycleModuleDO::getReceiptRecycleId,
//                        Collectors.toMap(ReceiptRecycleModuleDO::getOcrModuleDetailId, ReceiptRecycleModuleDO::getKeyValue)));
        Map<Long, Map<Long, String>> moduleValueMap = new HashMap<>();
        List<ReceiptRecycleModuleDO> collect = receiptRecycleModuleService.lambdaQuery()
                .in(ReceiptRecycleModuleDO::getReceiptRecycleId, receiptRecycleIdList)
                .in(CollUtil.isNotEmpty(entry.getValue()), ReceiptRecycleModuleDO::getOcrModuleDetailId, entry.getValue())
                .eq(ReceiptRecycleModuleDO::getIsEffective, IsEffectiveEnum.YES.getValue())
                .list().stream().filter(e -> ObjectUtil.isNotNull(e.getKeyValue()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            moduleValueMap = collect.stream().collect(Collectors.groupingBy(ReceiptRecycleModuleDO::getReceiptRecycleId,
                    Collectors.toMap(ReceiptRecycleModuleDO::getOcrModuleDetailId, ReceiptRecycleModuleDO::getKeyValue)));
        }
        if (CollUtil.isEmpty(moduleValueMap)) {
            return;
        }

        List<List<Object>> valueList = new ArrayList<>();
        for (Map<String, Object> recycleResultMap : recycleResultMapList) {
            List<Object> rowList = new ArrayList<>();
            form.getFieldList().forEach(field -> {
                Object value = recycleResultMap.getOrDefault(field, "");
                if (field.equals("weighType")) {
                    rowList.add(RecycleWeighTypeEnum.getByValue(Integer.valueOf(value.toString())).getDesc());
                } else {
                    rowList.add(value);
                }
            });
            Map<Long, String> moduleMap = moduleValueMap.get(Long.valueOf(recycleResultMap.get("id").toString()));
            entry.getValue().forEach(moduleId -> rowList.add(moduleMap.getOrDefault(moduleId, "")));
            rowList.add(recycleResultMap.get("attributionName"));
            rowList.add(recycleResultMap.get("attributionCode"));
            valueList.add(rowList);
        }
        writer.write(valueList, true);
    }
}
