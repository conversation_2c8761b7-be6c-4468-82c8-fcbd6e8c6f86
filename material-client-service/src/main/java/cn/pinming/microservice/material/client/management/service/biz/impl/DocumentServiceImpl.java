package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.infrastructure.annotation.RoleAuthority;
import cn.pinming.microservice.material.client.management.common.enums.RoleEnum;
import cn.pinming.microservice.material.client.management.common.form.DeveloperDocumentForm;
import cn.pinming.microservice.material.client.management.common.form.DocumentForm;
import cn.pinming.microservice.material.client.management.common.mapper.DeveloperDocumentMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ResourceDocumentMapper;
import cn.pinming.microservice.material.client.management.common.model.DeveloperDocumentDO;
import cn.pinming.microservice.material.client.management.common.model.ResourceDocumentDO;
import cn.pinming.microservice.material.client.management.common.vo.DocumentVO;
import cn.pinming.microservice.material.client.management.service.biz.DocumentService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.DownloadUrlOptionsDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.FileIdentityDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.PreviewResultDto;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DocumentServiceImpl implements DocumentService {

    @Resource
    private DeveloperDocumentMapper developerDocumentMapper;

    @Resource
    private ResourceDocumentMapper resourceDocumentMapper;

    @DubboReference
    private FileCenterService fileCenterService;

    @Resource
    private UserService userService;

    @RoleAuthority(value = RoleEnum.ADMIN)
    @Override
    public void developerDocumentCreate(DocumentForm documentForm) {
        DeveloperDocumentDO entity = new DeveloperDocumentDO();
        entity.setFileName(documentForm.getName());
        entity.setFileType(documentForm.getType());
        entity.setFileDesc(documentForm.getDesc());
        developerDocumentMapper.insert(entity);
    }

    @Override
    public List<DocumentVO> developerDocumentList() {
        List<DocumentVO> list = Lists.newArrayList();
        QueryWrapper<DeveloperDocumentDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().orderByDesc(DeveloperDocumentDO::getFileType);
        List<DeveloperDocumentDO> developerDocumentDOS = developerDocumentMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(developerDocumentDOS)) {
            list = developerDocumentDOS.stream().map(item -> {
                DocumentVO documentVO = new DocumentVO();
                documentVO.setId(item.getId());
                documentVO.setName(item.getFileName());
                documentVO.setType(item.getFileType());
                documentVO.setDesc(item.getFileDesc());
                documentVO.setTime(item.getGmtCreate());
                return documentVO;
            }).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public String developerDocumentContent(Long id) {
        DeveloperDocumentDO developerDocumentDO = developerDocumentMapper.selectById(id);
        return Optional.ofNullable(developerDocumentDO).orElse(new DeveloperDocumentDO()).getFileContent();
    }

    @Override
    public String developerDocumentDownload(String uuid, Date expireDate) {
        fileCenterService.addFileUuidsToFileServer(Collections.singletonList(uuid));
        FileIdentityDto fileIdentity = new FileIdentityDto();
        fileIdentity.setFileUuid(uuid);
        DownloadUrlOptionsDto options = new DownloadUrlOptionsDto();
        if (ObjectUtil.isNotNull(expireDate)) {
            options.setTime(expireDate);
        }
        return fileCenterService.acquireFileDownloadUrl(fileIdentity, options);
    }

    @Override
    public void developerDocumentDelete(Long id) {
        developerDocumentMapper.deleteById(id);
    }

    @RoleAuthority(value = RoleEnum.ADMIN)
    @Override
    public void resourceDocumentUpload(DocumentForm documentForm) {
        fileCenterService.addFileUuidsToFileServer(Collections.singletonList(documentForm.getUuid()));
        ResourceDocumentDO entity = new ResourceDocumentDO();
        entity.setFileName(documentForm.getName());
        entity.setFileUuid(documentForm.getUuid());
        entity.setFileType(documentForm.getType());
        entity.setFileDesc(documentForm.getDesc());
        resourceDocumentMapper.insert(entity);
    }

    @Override
    public List<DocumentVO> resourceDocumentList() {
        List<DocumentVO> list = Lists.newArrayList();
        QueryWrapper<ResourceDocumentDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().orderByDesc(ResourceDocumentDO::getGmtCreate);
        List<ResourceDocumentDO> resourceDocumentDOS = resourceDocumentMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(resourceDocumentDOS)) {
            list = resourceDocumentDOS.stream().map(item -> {
                DocumentVO documentVO = new DocumentVO();
                documentVO.setUuid(item.getFileUuid());
                documentVO.setName(item.getFileName());
                documentVO.setType(item.getFileType());
                documentVO.setDesc(item.getFileDesc());
                documentVO.setTime(item.getGmtCreate());
                return documentVO;
            }).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public String resourceDocumentPreview(String uuid) {
        FileIdentityDto identity = new FileIdentityDto();
        identity.setFileUuid(uuid);
        PreviewResultDto filePreviewResult = fileCenterService.findFilePreviewResult(identity);
        return Optional.ofNullable(filePreviewResult).orElse(new PreviewResultDto()).getFileUrl();
    }

    @Override
    public String resourceDocumentDownload(String uuid) {
        return fileCenterService.simpleAcquireFileDownloadUrl(uuid);
    }

    @Override
    public void resourceDocumentDelete(String uuid) {
        QueryWrapper<ResourceDocumentDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ResourceDocumentDO::getFileUuid, uuid);
        resourceDocumentMapper.delete(wrapper);
    }

    @Override
    public void developerDocumentSave(DeveloperDocumentForm documentForm) {
        DeveloperDocumentDO entity = new DeveloperDocumentDO();
        entity.setId(documentForm.getId());
        entity.setFileContent(documentForm.getContent());
        developerDocumentMapper.updateById(entity);
    }

}
