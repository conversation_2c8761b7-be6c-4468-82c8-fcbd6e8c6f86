package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.mapper.DeveloperAppMapper;
import cn.pinming.microservice.material.client.management.common.model.DeveloperAppDO;
import cn.pinming.microservice.material.client.management.service.biz.DeveloperAppService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class DeveloperAppServiceImpl extends ServiceImpl<DeveloperAppMapper, DeveloperAppDO> implements DeveloperAppService {
}
