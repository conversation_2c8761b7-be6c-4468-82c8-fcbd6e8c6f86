package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.form.AttributionAmountForm;
import cn.pinming.microservice.material.client.management.common.form.UserInvocationForm;
import cn.pinming.microservice.material.client.management.common.form.UserSpaceForm;
import cn.pinming.microservice.material.client.management.common.mapper.AppInvocationDailyLogMapper;
import cn.pinming.microservice.material.client.management.common.mapper.UserConfigMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.UserConfigExtMapper;
import cn.pinming.microservice.material.client.management.common.model.PurchaseLogDO;
import cn.pinming.microservice.material.client.management.common.model.UserConfigDO;
import cn.pinming.microservice.material.client.management.common.model.UserDO;
import cn.pinming.microservice.material.client.management.common.query.UserQuery;
import cn.pinming.microservice.material.client.management.common.vo.UserSpaceVO;
import cn.pinming.microservice.material.client.management.common.vo.UserSubscribeInfoVO;
import cn.pinming.microservice.material.client.management.common.vo.UserSubscribeVO;
import cn.pinming.microservice.material.client.management.infrastructure.constant.ConfigConstant;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.biz.PurchaseLogService;
import cn.pinming.microservice.material.client.management.service.biz.UserConfigService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import cn.pinming.springboot.starter.redis.util.RedisUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collections;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/7 17:05
 */
@Slf4j
@Service
public class UserConfigServiceImpl extends ServiceImpl<UserConfigMapper, UserConfigDO> implements UserConfigService {
    @Resource
    private AppInvocationDailyLogMapper appInvocationDailyLogExtMapper;
    @Resource
    private UserConfigExtMapper userConfigExtMapper;
    @Resource
    private PurchaseLogService purchaseLogService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private UserService userService;
    @Resource
    private DeviceAttributionService deviceAttributionService;

    @Override
    public IPage<UserSubscribeVO> subscribeList(UserQuery query) {
        IPage<UserSubscribeVO> page = userConfigExtMapper.selectSubscribeList(query);
        if (page == null || page.getRecords().isEmpty()) {
            return page;
        }
        return page.convert(record -> {
            record.setRemainingApi(this.getApiRemainingCount(record.getUid()));
            record.setRemainingReceiptApi(this.getReceiptApiRemainingCount(record.getUid()));
            return record;
        });
    }

    @Override
    public UserSpaceVO queryUserSpace(Long id) {
        UserConfigDO configDO = this.getById(id);
        UserSpaceVO result = new UserSpaceVO();
        BeanUtil.copyProperties(configDO, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editUserStorageConfig(UserSpaceForm form) {
        Long id = form.getId();
        UserConfigDO configDO = this.getById(id);
        //判断 租户当前业务数据已占空间大小是否小于所选容量，小于容量时校验失败。
        if (NumberUtil.isGreater(configDO.getSpaceUseSize(), form.getSpaceSize())) {
            throw new BizErrorException(BizExceptionMessageEnum.USER_SPACE_OVER);
        }

//        if (form.getSpaceExpire().isBefore(LocalDateTime.now())) {
//            throw new BizErrorException(BizExceptionMessageEnum.USER_SPACE_EXPIRE);
//        }

        configDO.setSpaceExpire(form.getSpaceExpire());
        configDO.setSpaceSize(form.getSpaceSize());
        this.updateById(configDO);

        //保存修改日志
        PurchaseLogDO purchaseLogDO = new PurchaseLogDO();
        purchaseLogDO.setUid(configDO.getUid());
        purchaseLogDO.setModifyType(1);
        purchaseLogDO.setModifySpace(form.getSpaceSize());
        purchaseLogDO.setSpaceExpire(form.getSpaceExpire());
        purchaseLogDO.setDescription(String.format("修改存储空间为%sG,有效期至%s", form.getSpaceSize(), LocalDateTimeUtil.format(form.getSpaceExpire(), "yyyy-MM-dd")));
        purchaseLogService.save(purchaseLogDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editUserInvocationConfig(UserInvocationForm form) {
        UserConfigDO configDO = this.lambdaQuery().eq(UserConfigDO::getUid, form.getUid()).one();
        long afterApiTotal;//累加
        LambdaUpdateChainWrapper<UserConfigDO> updateChainWrapper = this.lambdaUpdate().eq(UserConfigDO::getId, configDO.getId());
        if (form.getModifyType() == 2) {
            //称重数据服务总次数
            afterApiTotal = configDO.getApiTotal() + form.getModifyApiTotal() < 0 ? 0 : configDO.getApiTotal() + form.getModifyApiTotal();
            updateChainWrapper.eq(UserConfigDO::getApiTotal, configDO.getApiTotal())
                            .set(UserConfigDO::getApiTotal, afterApiTotal).update();
            //更新总次数后，删掉redis
            redisUtil.del(String.format(ConfigConstant.REMAINING_API_KEY, configDO.getUid()));

        } else if (form.getModifyType() == 3) {
            //单据匹配总次数
            afterApiTotal = configDO.getReceiptApiTotal() + form.getModifyApiTotal() < 0 ? 0 : configDO.getReceiptApiTotal() + form.getModifyApiTotal();
            updateChainWrapper.eq(UserConfigDO::getReceiptApiTotal, configDO.getReceiptApiTotal())
                    .set(UserConfigDO::getReceiptApiTotal, afterApiTotal).update();
            //更新总次数后，删掉redis
            redisUtil.del(String.format(ConfigConstant.REMAINING_RECEIPT_KEY, configDO.getUid()));
        } else {
            throw new BizErrorException(BizExceptionMessageEnum.QUERY_PARAM_NULL_ERROR);
        }

        //保存修改日志
        PurchaseLogDO purchaseLogDO = new PurchaseLogDO();
        purchaseLogDO.setUid(configDO.getUid());
        purchaseLogDO.setModifyType(form.getModifyType());
        purchaseLogDO.setModifyApiTotal(form.getModifyApiTotal());
        purchaseLogDO.setApiTotal(afterApiTotal);
        purchaseLogDO.setDescription(String.format("新增调用次数%s次，修改后总次数为%s", form.getModifyApiTotal(), afterApiTotal));
        purchaseLogService.save(purchaseLogDO);
    }

    @Override
    public UserSubscribeInfoVO queryUserSubscribeInfo(String uid) {
        UserConfigDO configDO = this.lambdaQuery().eq(UserConfigDO::getUid, uid).one();
        UserSubscribeInfoVO result = new UserSubscribeInfoVO();
        BeanUtil.copyProperties(configDO, result);
        LocalDateTime spaceExpire = result.getSpaceExpire();
        LocalDateTime now = LocalDateTimeUtil.now();
        Integer remainingDays = Math.toIntExact(LocalDateTimeUtil.between(now, spaceExpire, ChronoUnit.DAYS) + 1);
        result.setRemainingDays(remainingDays);
        result.setRemainingApi(this.getApiRemainingCount(uid));
        result.setRemainingReceiptApi(this.getReceiptApiRemainingCount(uid));

        //这个查询排除单据识别统计
        result.setList(userConfigExtMapper.selectAppInvocationList(uid, DeveloperAppEnum.weighDataService()));
        //只查询单据识别服务
        result.setReceiptInvocationList(appInvocationDailyLogExtMapper.selectExtAppInvocation(uid, DeveloperAppEnum.OCR.value()));
        return result;
    }

    @Override
    public void updateAttributionAmount(AttributionAmountForm form) {
        String uId = form.getUid();
        if (ObjectUtil.isNotNull(uId)) {
            UserDO one = userService.lambdaQuery()
                    .eq(UserDO::getUid, uId)
                    .one();
            if (ObjectUtil.isNotNull(one)) {
                // 修改归属方数量上限
                this.lambdaUpdate()
                        .eq(UserConfigDO::getUid, uId)
                        .set(UserConfigDO::getAttributionAmount, form.getAttributionAmount())
                        .set(UserConfigDO::getIsLimit, form.getIsLimit())
                        .update();
                // 修改归属方code
                if (CollUtil.isNotEmpty(form.getList())) {
                    deviceAttributionService.saveOrUpdateBatch(form.getList());
                }
            }
        }
    }

    @Override
    public Long getApiRemainingCount(String uid) {
        //获取redis称重数据服务剩余调用次数
        Object  apiRemaining = redisUtil.get(String.format(ConfigConstant.REMAINING_API_KEY, uid));
        if (apiRemaining != null) {
            return Long.valueOf(apiRemaining.toString());
        }
        UserConfigDO userConfigDO = this.lambdaQuery().eq(UserConfigDO::getUid, uid).one();
        if (userConfigDO == null) {
            return 0L;
        }
        Long sumUsedApiTotal = appInvocationDailyLogExtMapper.sumUsedApiTotal(uid, DeveloperAppEnum.weighDataService());
        userConfigDO.setApiUseTotal(sumUsedApiTotal);
        this.updateById(userConfigDO);
        //计算剩余次数
        long remaining = userConfigDO.getApiTotal() <= sumUsedApiTotal ? 0 : userConfigDO.getApiTotal() - sumUsedApiTotal;
        redisUtil.set(String.format(ConfigConstant.REMAINING_API_KEY, uid), remaining, 24 * 60 * 60L);//24小时
        return remaining;
    }


    @Override
    public Long getReceiptApiRemainingCount(String uid) {
        //获取redis剩余次数
        String key = String.format(ConfigConstant.REMAINING_RECEIPT_KEY, uid);
        Object apiUserTotal = redisUtil.get(key);
        if (apiUserTotal != null) {
            return Long.valueOf(apiUserTotal.toString());
        }
        UserConfigDO userConfigDO = this.lambdaQuery().eq(UserConfigDO::getUid, uid).one();
        if (userConfigDO == null) {
            return 0L;
        }
        Long sumUsedApiTotal = appInvocationDailyLogExtMapper.sumUsedApiTotal(uid, Collections.singletonList(DeveloperAppEnum.OCR.value()));
        userConfigDO.setReceiptApiUseTotal(sumUsedApiTotal);
        this.updateById(userConfigDO);
        //剩余次数
        long remaining = userConfigDO.getReceiptApiTotal() <= sumUsedApiTotal ? 0 : userConfigDO.getReceiptApiTotal() - sumUsedApiTotal;
        redisUtil.set(key, remaining, 24 * 60 * 60L);//24小时
        return remaining;
    }
}
