package cn.pinming.microservice.material.client.management.controller.selfcheck;


import cn.pinming.material.v2.model.WeighDataAssemble;
import cn.pinming.microservice.material.client.management.common.form.ConfirmPicForm;
import cn.pinming.microservice.material.client.management.common.form.DeliveryAddSelfCheckForm;
import cn.pinming.microservice.material.client.management.common.model.ext.WeighDataConfirmOriginExtDO;
import cn.pinming.microservice.material.client.management.common.query.DeliveryQuery;
import cn.pinming.microservice.material.client.management.common.query.SelfCheckQuery;
import cn.pinming.microservice.material.client.management.common.vo.SupplierConfigVO;
import cn.pinming.microservice.material.client.management.common.vo.h5.SimplePurchaseVO;
import cn.pinming.microservice.material.client.management.common.vo.selfcheck.SelfCheckDeliveryVO;
import cn.pinming.microservice.material.client.management.common.vo.selfcheck.SelfCheckModeConfigVO;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.service.biz.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.TreeMap;

/**
 * 基石司机自助称重确认客户端服务端接口.
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/8/23 17:10
 */
@Api(value = "self-check", tags = {"self-check"})
@RestController
@RequestMapping("/api/self-check")
public class SelfCheckController {

    @Resource
    private IDeliveryService deliveryService;
    @Resource
    private IWeighDataConfirmService weighDataConfirmService;
    @Resource
    private DeviceBindingService deviceBindingService;
    @Resource
    private ISelfQrcodeService selfQrcodeService;
    @Resource
    private IPurchaseOrderService purchaseOrderService;

    @ApiOperation(value = "获取司机自助确认终端模式")
    @GetMapping("/mode")
    public SingleResponse<?> mode(@RequestParam String deviceSn, @RequestParam String deviceType) {
        Integer result = deviceBindingService.getDeviceMode(deviceSn, deviceType);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "获取司机自助确认终端模式及相关配置")
    @GetMapping("/v2/mode")
    public SingleResponse<?> v2Mode(@RequestParam String deviceSn, @RequestParam String deviceType) {
        SelfCheckModeConfigVO result = deviceBindingService.getDeviceModeConfig(deviceSn, deviceType);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "根据设备sn获取供应商列表", response = SupplierConfigVO.class)
    @GetMapping("/supplier/list")
    public SingleResponse<?> supplierList(@RequestParam String deviceSn, @RequestParam String deviceType) {
        List<SupplierConfigVO> list = selfQrcodeService.listSupplierConfigByDeviceSn(deviceSn, deviceType);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "获取发货单列表", nickname = "qrcodePurchaseList", response = SimplePurchaseVO.class)
    @GetMapping("/purchase/list")
    public SingleResponse<?> purchaseList(@RequestParam String supplierExtId, @RequestParam Long attributionId) {
        List<SimplePurchaseVO> list = purchaseOrderService.listPurchaseOrder(supplierExtId, attributionId);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "获取发货单明细", nickname = "qrcodePurchaseDetail", response = SimplePurchaseVO.class)
    @GetMapping("/purchase/detail")
    public SingleResponse<?> purchaseDetail(@RequestParam Long purchaseId, @RequestParam Long attributionId) {
        SimplePurchaseVO result = purchaseOrderService.getPurchaseOrder(purchaseId, attributionId);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "保存发货单(运单)", nickname = "selfCheckDeliveryAdd")
    @PostMapping("/delivery/add")
    public SingleResponse<String> deliveryAdd(@Validated @RequestBody DeliveryAddSelfCheckForm form) {
        String deliveryNo = deliveryService.selfCheckAdd(form);
        return SingleResponse.of(deliveryNo);
    }

    @ApiOperation(value = "获取运单数据详情", response = SelfCheckDeliveryVO.class)
    @PostMapping("/delivery/detail")
    public SingleResponse<?> deliveryDetail(@Validated @RequestBody SelfCheckQuery query) {
        SelfCheckDeliveryVO result = deliveryService.getDeliveryDetail(query);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "保存OCR发货单(运单)", nickname = "selfCheckOCRDeliveryAdd")
    @PostMapping("/ocr/delivery/add")
    public SingleResponse<String> ocrDeliveryAdd(@RequestParam String deviceSn, @RequestParam String deviceType) {
        String deliveryNo = deliveryService.selfCheckOCRAdd(deviceSn, deviceType);
        return SingleResponse.of(deliveryNo);
    }

    @ApiOperation(value = "获取设备密码")
    @GetMapping("/{sn}/pwd")
    public SingleResponse<?> pwd(@PathVariable String sn, @RequestParam(required = false) String deviceType) {
        String result = deviceBindingService.getDevicePwd(sn, deviceType);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "确认单保存")
    @PostMapping("/confirm/save")
    public SingleResponse<?> save(@RequestBody WeighDataConfirmOriginExtDO form) {
        weighDataConfirmService.add(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "确认单-获取称重数据")
    @GetMapping("/confirm/getData/{deviceSn}/{record1}/{record2}")
    public SingleResponse<WeighDataAssemble> getData(@PathVariable("deviceSn") String deviceSn, @PathVariable("record1") String record1, @PathVariable("record2") String record2) {
        WeighDataAssemble vo = weighDataConfirmService.getData(deviceSn, record1, record2);
        return SingleResponse.of(vo);
    }

    @ApiOperation(value = "确认单照片保存")
    @PostMapping("/confirmPic/save")
    public SingleResponse<?> savePic(@Validated @RequestBody ConfirmPicForm form) {
        weighDataConfirmService.savePic(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "按模式查询最新一条运单数据", response = WeighDataConfirmOriginExtDO.class)
    @PostMapping("/delivery")
    public SingleResponse<?> delivery(@Validated @RequestBody DeliveryQuery query) {
        WeighDataConfirmOriginExtDO result = deliveryService.delivery(query);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "判断同一车牌是否小于称重最短间隔 ",response = Boolean.class)
    @PostMapping("/truck/duration")
    public SingleResponse<?> truckDuration(@Validated @RequestBody DeliveryQuery query) {
        boolean allowWeighing = deliveryService.checkAllowWeighing(query);
        return SingleResponse.of(allowWeighing);
    }

    @ApiOperation(value = "保存3型(仅毛皮重)发货单(运单)", nickname = "selfCheckWeighDeliveryAdd")
    @PostMapping("/weigh/delivery/add")
    public SingleResponse<String> weighDeliveryAdd(@RequestParam String deviceSn, @RequestParam String deviceType, @RequestParam String truckNo) {
        String deliveryNo = deliveryService.selfCheckWeighAdd(deviceSn, deviceType, truckNo);
        return SingleResponse.of(deliveryNo);
    }

    @ApiOperation(value = "根据设备sn获取供应商分组列表", response = SupplierConfigVO.class)
    @GetMapping("/supplier/group/list")
    public SingleResponse<?> supplierGroupList(@RequestParam String deviceSn, @RequestParam String deviceType) {
        TreeMap<String, List<SupplierConfigVO>> result = selfQrcodeService.supplierConfigMapByDeviceSnAndType(deviceSn, deviceType);
        return SingleResponse.of(result);
    }

}
