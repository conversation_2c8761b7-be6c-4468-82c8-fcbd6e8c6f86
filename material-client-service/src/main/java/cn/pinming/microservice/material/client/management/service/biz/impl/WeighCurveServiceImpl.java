package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.common.dto.WechatMsgDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataCheckDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataSavedDTO;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeviceTypeEnum;
import cn.pinming.microservice.material.client.management.common.enums.MessageRobotTypeEnum;
import cn.pinming.microservice.material.client.management.common.enums.MsgRobotTypeEnum;
import cn.pinming.microservice.material.client.management.common.form.CurveForm;
import cn.pinming.microservice.material.client.management.common.mapper.WeighCurveConfigMapper;
import cn.pinming.microservice.material.client.management.common.mapper.WeighCurveMapper;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.common.vo.WeighCurveVO;
import cn.pinming.microservice.material.client.management.infrastructure.util.MsgSendUtil;
import cn.pinming.microservice.material.client.management.service.biz.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 * 原始记录称重曲线 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Slf4j
@Service
public class WeighCurveServiceImpl extends ServiceImpl<WeighCurveMapper, WeighCurveDO> implements IWeighCurveService {
    @Resource
    private WeighDataService weighDataService;
    @Resource
    private IWeighCurveConfigService weighCurveConfigService;
    @Resource
    private IWeighCurveAlarmService weighCurveAlarmService;
    @Resource
    private ICheatAlarmLogService cheatAlarmLogService;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private DeviceBindingService deviceBindingService;
    @Resource
    private IMsgRobotConfigService msgRobotConfigService;
    @Resource
    private WeighCurveConfigMapper weighCurveConfigMapper;
    @Value("${h5.alarm.url}")
    private String h5AlarmUrl;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upload(CurveForm form, String deviceType) {
        String weighCurveId = form.getWeighCurveId();
        Integer count = lambdaQuery().eq(WeighCurveDO::getWeighCurveId, weighCurveId).count();
        if (count > 0) {
            return;
        }

        String deviceSn = form.getDeviceSn();
        WeighDataCheckDTO dto = weighDataService.check(deviceSn, DeveloperAppEnum.UPLOAD.value(), false, StrUtil.isNotBlank(deviceType) ? deviceType : DeviceTypeEnum.WEIGH.name());

        WeighCurveDO weighCurveDO = new WeighCurveDO();
        BeanUtils.copyProperties(form, weighCurveDO);
        weighCurveDO.setUid(dto.getUid());
        weighCurveDO.setAttributionId(dto.getAttributionId());
        weighCurveDO.setRecordId(form.getWeighDataId());
        weighCurveDO.setPushState(1); // 设置默认推送状态为未推送

        JSONObject jsonObj = JSONUtil.parseObj(form.getWeighCurveData());
        BigDecimal curveStartTime = jsonObj.getBigDecimal("curve_start_time");
        LocalDateTime startTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(NumberUtil.mul(curveStartTime, 1000).longValue()), ZoneId.systemDefault());
        weighCurveDO.setCreateTime(startTime);
        this.save(weighCurveDO);

        try {
            saveAlarmDataAndSendMessage(form, deviceSn, dto, weighCurveDO, startTime, jsonObj);
        } catch (Exception e) {
            log.error("保存曲线报警数据失败", e);
        }
    }

    //    @SneakyThrows
    public void saveAlarmDataAndSendMessage(CurveForm form, String deviceSn, WeighDataCheckDTO dto, WeighCurveDO weighCurveDO, LocalDateTime startTime, JSONObject jsonObj) {
        // 查询设备配置
        WeighCurveConfigDO weighCurveConfig = weighCurveConfigMapper.getWeighCurveConfig(dto.getDeviceId(), dto.getAttributionId(), dto.getDeviceBindingId());
        if (weighCurveConfig != null) {
            // 判断是否已保存过alarm数据 如果已保存过，则走更新逻辑
            WeighCurveAlarmDO weighCurveAlarm = weighCurveAlarmService.lambdaQuery().eq(WeighCurveAlarmDO::getRecordId, form.getWeighDataId()).one();
            List<String> errorMsgList = new ArrayList<>();
            JSONArray data = jsonObj.getJSONArray("data");
            JSONArray jsonArray = data.getJSONArray(data.size() - 1);
            BigDecimal timeDiff = jsonArray.getBigDecimal(1);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            boolean isExist = false;
            if (weighCurveAlarm == null) {
                // 走新增逻辑
                weighCurveAlarm = new WeighCurveAlarmDO();
                //保存称重曲线特征数据
                BeanUtils.copyProperties(weighCurveConfig, weighCurveAlarm, "id");
                weighCurveAlarm.setWeighCurveId(form.getWeighCurveId());
                weighCurveAlarm.setStartTime(startTime);
                weighCurveAlarm.setCurveId(form.getWeighCurveId());
                weighCurveAlarm.setEndTime(startTime.plusSeconds(timeDiff.intValue()));
                weighCurveAlarm.setRecordId(form.getWeighDataId());
                JSONArray weighDataSaved = jsonObj.getJSONArray("weigh_data_saved");
                BigDecimal weight = weighDataSaved.getBigDecimal(0);
                String unit = weighDataSaved.getStr(1);
                BigDecimal maxValue;
                if (data.size() == 1) {
                    maxValue = data.getJSONArray(0).getBigDecimal(0);
                } else {
                    maxValue = IntStream.range(0, data.size() - 1).mapToObj(i -> data.getJSONArray(i).getBigDecimal(0)) // 提取下标为 1 的元素
                            .max(Comparator.naturalOrder()).orElseThrow(() -> new RuntimeException("数组为空"));
                }
                weighCurveAlarm.setWeightMax(maxValue);
                weighCurveAlarm.setWeightDiff(NumberUtil.sub(weight, maxValue).setScale(4, RoundingMode.HALF_UP));

                // 当前曲线开始时间是否早于同一设备上发生的本次曲线数据上一次曲线数据的结束时间
                WeighCurveDO one = lambdaQuery().eq(WeighCurveDO::getDeviceSn, deviceSn).eq(WeighCurveDO::getAttributionId, dto.getAttributionId()).ne(WeighCurveDO::getWeighCurveId, weighCurveDO.getWeighCurveId()).le(WeighCurveDO::getCreateTime, startTime).orderByDesc(WeighCurveDO::getCreateTime).last("limit 1").one();
                if (one != null) { // 是否跟车
                    String weighCurveId = one.getWeighCurveId();
                    WeighCurveAlarmDO one1 = weighCurveAlarmService.lambdaQuery().eq(WeighCurveAlarmDO::getWeighCurveId, weighCurveId).one();
                    if (one1 != null && one1.getEndTime().isAfter(startTime)) {
                        weighCurveAlarm.setFollowTruck(1);
                    }
                }

                // 称重开始前 是否有报警日志
                CheatAlarmLogDO alarmLog = cheatAlarmLogService.lambdaQuery().eq(CheatAlarmLogDO::getDeviceSn, deviceSn)
                        .ge(CheatAlarmLogDO::getCreateTime, startTime.minusSeconds(weighCurveConfig.getAlarmTime()))
                        .orderByDesc(CheatAlarmLogDO::getCreateTime)
                        .last("limit 1")
                        .one();
                weighCurveAlarm.setLastAlarmTime(alarmLog == null ? null : alarmLog.getCreateTime());
                // 称重期间 是否有报警日志
                long alarmCount = cheatAlarmLogService.lambdaQuery()
                        .eq(CheatAlarmLogDO::getDeviceSn, deviceSn)
                        .between(CheatAlarmLogDO::getCreateTime, weighCurveAlarm.getStartTime(), weighCurveAlarm.getEndTime())
                        .count();
                weighCurveAlarm.setAlarmPeriod(alarmCount > 0 ? 1 : 0);

                // 遍历数组，检查时间偏移量差值
                Set<String> platformData = new TreeSet<>();
                if (data.size() == 1) {
                    JSONArray current = data.getJSONArray(0);
                    BigDecimal time = current.getBigDecimal(1);
                    BigDecimal weigh = current.getBigDecimal(0);
                    LocalDateTime nextTimeLocal = startTime.plusNanos(time.multiply(new BigDecimal(1000000000)).longValue());
                    String format = StrUtil.format("{}{},{} - {},{}秒", weigh, unit, startTime.format(dateTimeFormatter), nextTimeLocal.format(dateTimeFormatter), NumberUtil.roundHalfEven(time, 0));
                    platformData.add(format);
                } else {
                    for (int i = 0; i < data.size() - 1; i++) {
                        JSONArray current = data.getJSONArray(i);
                        JSONArray next = data.getJSONArray(i + 1);
                        BigDecimal currentTime = current.getBigDecimal(1); // 当前时间偏移量
                        BigDecimal nextTime = next.getBigDecimal(1);       // 下一个时间偏移量
                        if (NumberUtil.isGreaterOrEqual(nextTime.subtract(currentTime), BigDecimal.valueOf(weighCurveConfig.getPlatformDuration()))) {
                            LocalDateTime localDateTime = startTime.plusNanos(currentTime.multiply(new BigDecimal(1000000000)).longValue());
                            LocalDateTime nextTimeLocal = startTime.plusNanos(nextTime.multiply(new BigDecimal(1000000000)).longValue());
                            String format = StrUtil.format("{}{},{} - {},{}秒", next.get(0), unit, localDateTime.format(dateTimeFormatter), nextTimeLocal.format(dateTimeFormatter), NumberUtil.roundHalfEven(NumberUtil.sub(nextTime, currentTime), 0));
                            platformData.add(format);
                        }
                    }
                }

                if (CollUtil.isNotEmpty(platformData)) {
                    weighCurveAlarm.setPlatformData(StrUtil.join("/", platformData));
                }

                // 根据结果 判断触发告警，并发送消息通知
                int platform = platformData.size();
                if (weighCurveConfig.getPlatformCount() < platform) {
                    errorMsgList.add("平台期");
                }
                long diffTime = LocalDateTimeUtil.between(startTime, weighCurveAlarm.getEndTime(), ChronoUnit.SECONDS);
                if (diffTime > weighCurveConfig.getSustainDuration()) {
                    errorMsgList.add("曲线时长");
                }
                BigDecimal weightDiff = weighCurveAlarm.getWeightDiff().abs();
                if ("吨".equals(unit)) { // 单位为吨时，转换为kg
                    weightDiff = weighCurveAlarm.getWeightDiff().multiply(new BigDecimal(1000));
                    weighCurveAlarm.setWeightDiff(weightDiff);
                }
                if (NumberUtil.isGreater(weightDiff, weighCurveConfig.getWeight().abs())) {
                    errorMsgList.add("称重最值差");
                }
                if (alarmLog != null) {
                    errorMsgList.add("防控仪监控");
                }
                if (weighCurveAlarm.getFollowTruck() != null && weighCurveAlarm.getFollowTruck() == 1) {
                    errorMsgList.add("是否跟车");
                }
                weighCurveAlarm.setAlarmItem(weighCurveConfig.getAlarmItem());
                if (CollUtil.isNotEmpty(errorMsgList)) {
                    weighCurveAlarm.setAlarmReason(StrUtil.join(StrUtil.COMMA, errorMsgList));
                    String alarmItem = weighCurveConfig.getAlarmItem();
                    if (StrUtil.isNotBlank(alarmItem)) {
                        List<String> alarmItemList = StrUtil.split(alarmItem, StrUtil.COMMA);
                        // 如果errorMsgList和alarmItemList有交集，则说明触发了多个告警项
                        if (CollUtil.containsAny(errorMsgList, alarmItemList)) {
                            weighCurveAlarm.setAlarmLevel(1);
                        } else {
                            weighCurveAlarm.setAlarmLevel(2);
                        }
                    }
                }

            } else {
                // 走更新逻辑
                isExist = true;
                // 查询所有的曲线数据
                String recordId = form.getWeighDataId();
                List<WeighCurveDO> curveList = lambdaQuery().eq(WeighCurveDO::getRecordId, recordId)
                        .select(WeighCurveDO::getWeighCurveData, WeighCurveDO::getWeighCurveId).orderByAsc(WeighCurveDO::getCreateTime).list();

                List<String> curveIdList = curveList.stream().map(WeighCurveDO::getWeighCurveId).distinct().collect(Collectors.toList());
//                curveIdList.add(form.getWeighCurveId());
                weighCurveAlarm.setCurveId(StrUtil.join(",", curveIdList));
                //保存称重曲线特征数据
                BeanUtils.copyProperties(weighCurveConfig, weighCurveAlarm, "id");
                weighCurveAlarm.setWeighCurveId(weighCurveDO.getWeighCurveId());
                weighCurveAlarm.setRecordId(form.getWeighDataId());
                weighCurveAlarm.setEndTime(startTime.plusSeconds(timeDiff.intValue()));

                //计算曲线特征数据
                JSONArray weighDataSaved = jsonObj.getJSONArray("weigh_data_saved");
                BigDecimal weight = weighDataSaved.getBigDecimal(0);
                String unit = weighDataSaved.getStr(1);
                BigDecimal maxValue;
                // 组装所有曲线数据
                JSONArray totalData = new JSONArray();
                for (WeighCurveDO curveDO : curveList) {
                    JSONObject jsonObject = JSONUtil.parseObj(curveDO.getWeighCurveData());
                    JSONArray weighData = jsonObject.getJSONArray("data");
                    BigDecimal curveStartTime = jsonObject.getBigDecimal("curve_start_time");
                    for (int i = 0; i < weighData.size(); i++) {
                        JSONArray jsonArray1 = weighData.getJSONArray(i);
                        jsonArray1.put(1, NumberUtil.add(jsonArray1.getBigDecimal(1), curveStartTime));
                        totalData.addAll(weighData);
                    }
                }
                if (totalData.size() == 1) {
                    maxValue = totalData.getJSONArray(0).getBigDecimal(0);
                } else {
                    maxValue = IntStream.range(0, totalData.size() - 1).mapToObj(i -> totalData.getJSONArray(i).getBigDecimal(0)) // 提取下标为 1 的元素
                            .max(Comparator.naturalOrder()).orElseThrow(() -> new RuntimeException("数组为空"));
                }

                weighCurveAlarm.setWeightMax(maxValue);
                weighCurveAlarm.setWeightDiff(NumberUtil.sub(weight, maxValue).setScale(4, RoundingMode.HALF_UP));
                // 计算平台期

                // 称重期间 是否有报警日志
                long alarmCount = cheatAlarmLogService.lambdaQuery()
                        .eq(CheatAlarmLogDO::getDeviceSn, deviceSn)
                        .between(CheatAlarmLogDO::getCreateTime, weighCurveAlarm.getStartTime(), weighCurveAlarm.getEndTime())
                        .count();
                weighCurveAlarm.setAlarmPeriod(alarmCount > 0 ? 1 : 0);

                // 遍历数组，检查时间偏移量差值
                Set<String> platformData = new TreeSet<>();
                if (totalData.size() == 1) {
                    JSONArray current = data.getJSONArray(0);
                    BigDecimal time = current.getBigDecimal(1);
                    BigDecimal weigh = current.getBigDecimal(0);
                    LocalDateTime nextTimeLocal = startTime.plusNanos(time.multiply(new BigDecimal(1000000000)).longValue());
                    String format = StrUtil.format("{}{},{} - {},{}秒", weigh, unit, startTime.format(dateTimeFormatter), nextTimeLocal.format(dateTimeFormatter), NumberUtil.roundHalfEven(time, 0));
                    platformData.add(format);
                } else {
                    for (int i = 0; i < totalData.size() - 1; i++) {
                        JSONArray current = totalData.getJSONArray(i);
                        JSONArray next = totalData.getJSONArray(i + 1);
                        BigDecimal currentTime = current.getBigDecimal(1); // 当前时间偏移量
                        BigDecimal nextTime = next.getBigDecimal(1);       // 下一个时间偏移量
                        if (NumberUtil.isGreaterOrEqual(nextTime.subtract(currentTime), BigDecimal.valueOf(weighCurveConfig.getPlatformDuration()))) {
                            // 时间戳转localDateTime
                            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(currentTime.longValue()), ZoneId.systemDefault());
                            LocalDateTime nextTimeLocal = LocalDateTime.ofInstant(Instant.ofEpochSecond(nextTime.longValue()), ZoneId.systemDefault());
                            String format = StrUtil.format("{}{},{} - {},{}秒", next.get(0), unit, localDateTime.format(dateTimeFormatter), nextTimeLocal.format(dateTimeFormatter), NumberUtil.roundHalfEven(NumberUtil.sub(nextTime, currentTime), 0));
                            platformData.add(format);
                        }
                    }
                }
                if (CollUtil.isNotEmpty(platformData)) {
                    if (StrUtil.isNotBlank(weighCurveAlarm.getPlatformData())) {
                        // 合并平台期
                        List<String> split = StrUtil.split(weighCurveAlarm.getPlatformData(), "/");
                        split.addAll(platformData);
                        weighCurveAlarm.setPlatformData(StrUtil.join("/", split));
                    } else {
                        weighCurveAlarm.setPlatformData(StrUtil.join("/", platformData));
                    }
                }

                int platform = weighCurveAlarm.getPlatformData().split("/").length;
                if (weighCurveConfig.getPlatformCount() < platform) {
                    errorMsgList.add("平台期");
                }
                long diffTime = LocalDateTimeUtil.between(startTime, weighCurveAlarm.getEndTime(), ChronoUnit.SECONDS);
                if (diffTime > weighCurveConfig.getSustainDuration()) {
                    errorMsgList.add("曲线时长");
                }
                BigDecimal weightDiff = weighCurveAlarm.getWeightDiff().abs();
                if ("吨".equals(unit)) { // 单位为吨时，转换为kg
                    weightDiff = weighCurveAlarm.getWeightDiff().multiply(new BigDecimal(1000));
                    weighCurveAlarm.setWeightDiff(weightDiff);
                }
                if (NumberUtil.isGreater(weightDiff, weighCurveConfig.getWeight().abs())) {
                    errorMsgList.add("称重最值差");
                }
                if (weighCurveAlarm.getFollowTruck() != null && weighCurveAlarm.getFollowTruck() == 1) {
                    errorMsgList.add("是否跟车");
                }
                weighCurveAlarm.setAlarmItem(weighCurveConfig.getAlarmItem());
                if (CollUtil.isNotEmpty(errorMsgList)) {
                    weighCurveAlarm.setAlarmReason(StrUtil.join(StrUtil.COMMA, errorMsgList));
                    String alarmItem = weighCurveConfig.getAlarmItem();
                    if (StrUtil.isNotBlank(alarmItem)) {
                        List<String> alarmItemList = StrUtil.split(alarmItem, StrUtil.COMMA);
                        // 如果errorMsgList和alarmItemList有交集，则说明触发了多个告警项
                        if (CollUtil.containsAny(errorMsgList, alarmItemList)) {
                            weighCurveAlarm.setAlarmLevel(1);
                        } else {
                            weighCurveAlarm.setAlarmLevel(2);
                        }
                    }
                }

            }

            if (isExist) {
                log.info("更新称重曲线报警数据");
                weighCurveAlarmService.updateById(weighCurveAlarm);
            } else {
                log.info("新增称重曲线报警数据");
                weighCurveAlarmService.save(weighCurveAlarm);
                // 如果触发了警报，更新称重记录的cheat_alarm_id
                String cheatAlarmId = weighCurveAlarm.getId().toString();
                log.info("更新称重记录的cheat_alarm_id");
                weighDataService.updateCheatAlarmId(form.getWeighDataId(), cheatAlarmId);
            }

            sendRobotMessage(form, dto, errorMsgList);
        }
    }

    private void sendRobotMessage(CurveForm form, WeighDataCheckDTO dto, List<String> errorMsgList) {
        if (CollUtil.isNotEmpty(errorMsgList)) {
            String errorMsg = StrUtil.join(StrUtil.COMMA, errorMsgList);
            DeviceAttributionDO attributionDO = deviceAttributionService.getById(dto.getAttributionId());
            String name = attributionDO.getName();
            // 查询配置 发现企业 or 钉钉消息推送
            DeviceBindingDO bindingDO = deviceBindingService.lambdaQuery().eq(DeviceBindingDO::getDeviceId, dto.getDeviceId()).eq(DeviceBindingDO::getAttributionId, dto.getAttributionId()).one();
            if (bindingDO != null && StrUtil.isNotBlank(bindingDO.getMsgRobot()) && StrUtil.isNotBlank(bindingDO.getMsgReceiver())) {
                String msgRobot = bindingDO.getMsgRobot();
                String msgReceiver = bindingDO.getMsgReceiver();
                List<String> robotIdList = StrUtil.split(msgRobot, StrUtil.COMMA);

                List<MsgRobotConfigDO> robotConfigList = msgRobotConfigService.lambdaQuery().in(MsgRobotConfigDO::getId, robotIdList).list();
                if (CollUtil.isNotEmpty(robotConfigList)) {
                    robotConfigList.forEach(robotConfig -> {
                        Integer type = robotConfig.getType();
                        String scope = robotConfig.getScope();
                        if (StrUtil.isNotBlank(scope)) {
                            List<String> scopeList = StrUtil.split(scope, StrUtil.COMMA);
                            if (scopeList.contains(MsgRobotTypeEnum.M1.getVal().toString())) {
                                String message = StrUtil.format("【{}】发生称重作弊风险：\r\n其中【{}】指标项异常\r\n相关称重记录ID:{}\r\n详情请点击:{}\r\n", name, errorMsg, form.getWeighDataId(), StrUtil.format(h5AlarmUrl, form.getWeighDataId()));
                                //mentioned_mobile_list
                                try {
                                    if (Objects.equals(type, MessageRobotTypeEnum.DING_DING.getVal())) {
                                        String content = "{\"msgtype\": \"text\",\"text\": {\"content\":\"" + message + "\"}}";
                                        MsgSendUtil.sendDingDingMsg(robotConfig.getToken(), robotConfig.getSecret(), content);
                                    } else if (Objects.equals(type, MessageRobotTypeEnum.WECHAT.getVal())) {
                                        WechatMsgDTO wechatMsgDTO = new WechatMsgDTO();
                                        WechatMsgDTO.WechatText text = new WechatMsgDTO.WechatText();
                                        text.setContent(message);
                                        text.setMentioned_mobile_list(StrUtil.split(msgReceiver, StrUtil.COMMA));
                                        wechatMsgDTO.setText(text);
                                        String content = JSONUtil.toJsonStr(wechatMsgDTO);
                                        MsgSendUtil.sendWechatMsg(robotConfig.getToken(), content);
                                    }
                                } catch (Exception e) {
                                    log.error("消息推送失败：{}", e.getMessage());
                                }
                            }
                        }
                    });
                }
            }
        }
    }

    @Override
    public WeighCurveVO detail(String id) {
        List<WeighCurveDO> list = this.lambdaQuery().eq(WeighCurveDO::getRecordId, id).list();
        WeighCurveVO vo = new WeighCurveVO();
        List<Double> weights = new ArrayList<>();
        List<LocalDateTime> times = new ArrayList<>();
        List<WeighDataSavedDTO> points = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(e -> {
                String weighCurveData = e.getWeighCurveData();
                try {
                    handle(weights, times, points, weighCurveData, e.getCreateTime());
                } catch (JsonProcessingException ex) {
                    throw new RuntimeException(ex);
                }
            });
        }
        if (CollUtil.isNotEmpty(points)) {
            vo.setWeighPoints(points.stream().collect(Collectors.collectingAndThen(Collectors.toMap(WeighDataSavedDTO::getWeighDataId, p -> p, (existing, replacement) -> existing), map -> new ArrayList<>(map.values()))));
        }
        vo.setWeights(weights);
        vo.setTimes(times);

        if (CollUtil.isNotEmpty(times)) {
            String deviceSn = list.get(0).getDeviceSn();
            LocalDateTime startTime = times.get(0);
            LocalDateTime endTime = times.get(times.size() - 1);
            List<CheatAlarmLogDO> logList = cheatAlarmLogService.lambdaQuery()
                    .eq(CheatAlarmLogDO::getDeviceSn, deviceSn)
                    .between(CheatAlarmLogDO::getCreateTime, startTime, endTime)
                    .list();
            if (CollUtil.isNotEmpty(logList)) {
                List<LocalDateTime> collect = logList.stream().map(CheatAlarmLogDO::getCreateTime).collect(Collectors.toList());
                vo.setLogTimes(collect);
            }
        }

        return vo;
    }

    public void handle(List<Double> weights, List<LocalDateTime> times, List<WeighDataSavedDTO> points, String jsonStr, LocalDateTime createTime) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonStr);
        JsonNode data = root.get("data");
        JsonNode weighDataSaved = root.get("weigh_data_saved");
        LocalDateTime preTime = null;

        for (int i = 0; i < data.size(); i++) {
            double weight = data.get(i).get(0).asDouble();
            double dif = data.get(i).get(1).asDouble();

            BigDecimal secondsBigDecimal = new BigDecimal(dif);
            int integerSeconds = secondsBigDecimal.setScale(0, RoundingMode.HALF_UP).intValue();
            BigDecimal fractionalSeconds = secondsBigDecimal.subtract(BigDecimal.valueOf(integerSeconds)).multiply(BigDecimal.valueOf(1_000_000_000)).setScale(0, RoundingMode.HALF_UP);
            Instant instant = createTime.atZone(ZoneOffset.UTC).toInstant().plusSeconds(integerSeconds).plusNanos(fractionalSeconds.intValue());
            LocalDateTime localDateTime = instant.atZone(ZoneOffset.UTC).toLocalDateTime();

            if (i == 0 && createTime != localDateTime) {
                weights.add(weight);
                times.add(createTime);
            }
            if (i != 0) {
                weights.add(weight);
                times.add(preTime.plus(1, ChronoUnit.MILLIS));
            }
            weights.add(weight);
            times.add(localDateTime);
            preTime = localDateTime;
        }

        if (ObjectUtil.isNotNull(weighDataSaved)) {
            WeighDataSavedDTO point = new WeighDataSavedDTO();
            double weightPoint = weighDataSaved.get(0).asDouble();
            String unit = weighDataSaved.get(1).asText();
            LocalDateTime time = LocalDateTime.parse(weighDataSaved.get(2).asText());
            String id = weighDataSaved.get(3).asText();

            point.setWeighDataId(id);
            point.setWeight(weightPoint);
            point.setUnit(unit);
            point.setTime(time);

            points.add(point);
        }
    }


    public static void main(String[] args) {
        JSONArray totalData = new JSONArray();
        String jsonStr = "{\"data\": [[9.04, 19.2001268863678], [9.06, 20.239391565322876], [9.04, 61.05601930618286], [9.06, 61.05601930618286], [9.08, 61.582319021224976], [9.1, 62.69663310050965], [9.08, 62.69663310050965], [9.06, 63.99817895889282], [9.04, 65.54666352272034], [9.06, 66.06976747512817], [9.08, 67.15985059738159], [9.1, 68.44649529457092], [9.08, 69.2199137210846], [9.1, 69.74469304084778], [9.08, 72.67304062843323], [9.1, 72.67304062843323], [9.08, 73.18708968162537], [9.06, 74.82855153083801], [9.08, 74.82855153083801], [9.1, 75.35531640052795], [9.08, 75.88255834579468], [9.06, 76.47170901298523], [9.04, 78.61448359489441], [9.06, 79.71395754814148], [9.04, 81.34171056747437], [9.02, 84.78952717781067], [9.04, 87.19222402572632], [9.02, 87.71828246116638], [9.04, 92.62967109680176], [9.02, 96.61261010169984], [9.04, 99.53160071372986], [9.02, 100.30477285385132], [9.04, 107.55305433273315], [9.12, 107.55305433273315], [9.18, 107.55305433273315], [9.2, 108.08035922050476], [9.18, 108.60755705833436], [9.06, 108.60755705833436], [9.02, 108.67055654525755], [9.0, 109.19598841667175], [9.02, 109.19598841667175], [9.0, 109.7239511013031], [9.02, 109.7239511013031], [9.12, 110.25172543525696], [9.14, 110.77635765075684], [9.2, 110.77635765075684], [9.18, 110.83722376823424], [9.2, 111.36329913139345], [9.18, 111.36329913139345], [9.16, 111.8887255191803], [9.08, 111.8887255191803], [9.0, 112.13586640357973], [8.96, 112.64936423301695], [8.98, 112.64936423301695], [8.92, 113.1750602722168], [8.96, 113.1750602722168], [8.98, 113.23651838302612], [9.08, 113.7485511302948], [9.16, 113.7485511302948], [9.18, 114.27386736869812], [8.68, 114.27386736869812], [5.86, 114.27386736869812], [4.88, 114.80054092407228], [4.58, 114.80054092407228], [4.38, 115.32804155349731], [4.26, 115.32804155349731], [4.1, 115.389794588089], [0.64, 115.9177656173706]], \"curve_start_time\": 1746476213.0520878, \"weigh_data_saved\": [16.74, \"吨\", \"2025-05-06T03:57:47.031000\", \"8e28c6d3b2424588bec1943d410a7b3f\"]}";
        String jsonStr1 = "{\"data\": [[8.98, 1.6212363243103027], [9.0, 7.956480264663696], [8.98, 9.259663105010986], [9.0, 9.785743713378906], [8.98, 13.054959058761597], [9.0, 15.208730459213257], [8.98, 17.375207662582397], [9.0, 18.42902421951294], [8.98, 20.63024592399597], [9.0, 22.21078824996948], [8.98, 22.799271821975708], [9.0, 24.376150131225582], [8.98, 24.437909603118896], [8.96, 60.45503377914429], [8.98, 60.967090129852295], [9.0, 60.967090129852295], [9.02, 62.06819725036621], [9.0, 62.65503120422363], [8.98, 63.182523250579834], [8.96, 64.2324571609497], [8.98, 64.2324571609497], [9.0, 64.29370832443237], [9.02, 68.84912991523743], [9.0, 69.89831733703613], [8.98, 70.41121125221252], [8.96, 78.75356149673462], [8.98, 80.0525004863739], [8.96, 115.07112526893616], [8.98, 117.14785408973694], [8.96, 121.44333243370056], [8.98, 186.64381766319275], [9.0, 189.5718936920166], [8.98, 193.02935218811035], [9.0, 242.1289658546448], [9.02, 242.65581512451172], [9.0, 245.90064668655396], [9.02, 259.5426414012909], [9.04, 260.0597894191742], [9.06, 260.1215937137604], [9.08, 271.16851806640625], [9.06, 271.698194026947], [9.04, 272.22484374046326], [9.02, 293.30658531188965], [9.04, 293.8205506801605], [9.06, 294.3450815677643], [9.08, 294.872750043869], [9.1, 295.46022748947144], [9.08, 295.98533606529236], [9.06, 296.51187777519226], [9.04, 300.31718134880066]], \"curve_start_time\": 1746475912.729633, \"weigh_data_saved\": [16.74, \"吨\", \"2025-05-06T03:57:47.031000\", \"8e28c6d3b2424588bec1943d410a7b3f\"]}";

        JSONObject jsonObject = JSONUtil.parseObj(jsonStr);
        JSONArray weighData1 = jsonObject.getJSONArray("data");
        totalData.addAll(weighData1);

        JSONObject jsonObject2 = JSONUtil.parseObj(jsonStr1);
        JSONArray weighData2 = jsonObject2.getJSONArray("data");
        totalData.addAll(weighData2);

        System.out.println(totalData.toString());
        BigDecimal maxValue;
        if (totalData.size() == 1) {
            maxValue = totalData.getJSONArray(0).getBigDecimal(0);
        } else {
            maxValue = IntStream.range(0, totalData.size() - 1).mapToObj(i -> totalData.getJSONArray(i).getBigDecimal(0)) // 提取下标为 1 的元素
                    .max(Comparator.naturalOrder()).orElseThrow(() -> new RuntimeException("数组为空"));
        }
        System.out.println(maxValue);
    }
}
