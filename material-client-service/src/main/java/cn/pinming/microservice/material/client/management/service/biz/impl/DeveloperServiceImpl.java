package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperTypeEnum;
import cn.pinming.microservice.material.client.management.common.form.GenerateForm;
import cn.pinming.microservice.material.client.management.common.mapper.DeveloperMapper;
import cn.pinming.microservice.material.client.management.common.model.DeveloperAppDO;
import cn.pinming.microservice.material.client.management.common.model.DeveloperDO;
import cn.pinming.microservice.material.client.management.common.model.UserDO;
import cn.pinming.microservice.material.client.management.common.model.ext.DeveloperExtDO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.AppSecretUtil;
import cn.pinming.microservice.material.client.management.service.biz.DeveloperAppService;
import cn.pinming.microservice.material.client.management.service.biz.DeveloperService;
import cn.pinming.microservice.material.client.management.service.biz.UserExtConfigService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import cn.pinming.springboot.starter.redis.util.RedisUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class DeveloperServiceImpl extends ServiceImpl<DeveloperMapper, DeveloperDO> implements DeveloperService {

    @Resource
    private DeveloperMapper developerExtMapper;
    @Resource
    private UserService userService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private DeveloperAppService developerAppService;
    @Resource
    private UserExtConfigService userExtConfigService;

    @Override
    public List<DeveloperExtDO> selectAppList(String uId) {
        return developerExtMapper.selectAppList(uId);
    }

    @Override
    public void updateDeveloperType(Long id, Byte type) {
        this.lambdaUpdate().eq(DeveloperDO::getId, id)
                .set(DeveloperDO::getType, type)
                .update();
        DeveloperDO developerDO = this.getById(id);
        if (Objects.equals(developerDO.getAppId(), DeveloperAppEnum.RECYCLE.value()) && developerDO.getType() == DeveloperTypeEnum.START.value()) {
            userExtConfigService.init(developerDO.getCreateId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generate(String uId, GenerateForm form) {
        String generateEmailCaptcha = UserService.EMAIL_GENERATE_CAPTCHA_REDIS_PREFIX + form.getEmail();
        String generateEmailCaptchaCache = (String) redisUtil.get(generateEmailCaptcha);
        if (StringUtils.isBlank(generateEmailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_INVALID_ERROR);
        }
        if (StringUtils.isBlank(form.getEmailCaptcha()) || !form.getEmailCaptcha().equals(generateEmailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_ERROR);
        }

        userService.lambdaUpdate().eq(UserDO::getUid, uId).set(UserDO::getAppSecretKey, AppSecretUtil.generate(32, true)).update(new UserDO());
        // 移除邮箱验证码缓存
        redisUtil.del(generateEmailCaptcha);
    }

    @Override
    public void init() {
        // 全量服务
        List<DeveloperAppDO> serviceList = developerAppService.list();
        if (CollUtil.isNotEmpty(serviceList)) {
            List<Long> serviceIdList = serviceList.stream().map(e -> e.getId()).collect(Collectors.toList());
            // 全量用户
            List<UserDO> userList = userService.list();
            if (CollUtil.isNotEmpty(userList)) {
                List<String> uidList = userList.stream().map(e -> e.getUid()).collect(Collectors.toList());
                // 用户已有
                List<DeveloperDO> developerDOList = this.lambdaQuery()
                        .in(DeveloperDO::getCreateId, uidList)
                        .list();
                if (CollUtil.isNotEmpty(developerDOList)) {
                    List<DeveloperDO> result = new ArrayList<>();
                    Map<String, List<DeveloperDO>> exit = developerDOList.stream().collect(Collectors.groupingBy(BaseDO::getCreateId));
                    uidList.stream().forEach(e -> {
                        List<DeveloperDO> exitByUid = exit.get(e);
                        if (CollUtil.isNotEmpty(exitByUid)) {
                            List<Long> col = exitByUid.stream().map(DeveloperDO::getAppId).collect(Collectors.toList());
                            List<Long> exclude = serviceIdList.stream().filter(item -> !col.contains(item)).collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(exclude)) {
                                List<DeveloperDO> add = exclude.stream().map(m -> {
                                    DeveloperDO developerDO = new DeveloperDO();
                                    developerDO.setAppId(m);
                                    developerDO.setCreateId(e);
                                    developerDO.setModifyId(e);
                                    return developerDO;
                                }).collect(Collectors.toList());
                                result.addAll(add);
                            }
                        }
                    });
                    this.saveBatch(result);
                }
            }
        }
    }
}
