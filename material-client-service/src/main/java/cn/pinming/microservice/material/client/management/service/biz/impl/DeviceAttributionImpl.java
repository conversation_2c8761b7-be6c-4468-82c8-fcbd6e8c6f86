package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.form.DeviceAttributionForm;
import cn.pinming.microservice.material.client.management.common.mapper.DeviceAttributionMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceAttributionExtMapper;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.DeviceBindingDO;
import cn.pinming.microservice.material.client.management.common.model.WeighDataDO;
import cn.pinming.microservice.material.client.management.common.vo.AttributionDeviceVO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.biz.DeviceBindingService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class DeviceAttributionImpl extends ServiceImpl<DeviceAttributionMapper, DeviceAttributionDO> implements DeviceAttributionService {
    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private DeviceBindingService deviceBindingService;
    @Resource
    private WeighDataService weighDataService;
    @Value("${user_default_pic}")
    private String userDefaultPic;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private DeviceAttributionExtMapper deviceAttributionExtMapper;

    @Override
    public void add(DeviceAttributionForm form) {
        String uId = userIdUtil.getUId();

        // 归属方code校验
        form.setCode(form.getCode().replaceAll(" ", ""));
        repeatCheck(form, uId);

        DeviceAttributionDO deviceAttributionDO = new DeviceAttributionDO();
        BeanUtils.copyProperties(form, deviceAttributionDO);
        if (StrUtil.isBlank(form.getBase64()) && ObjectUtil.isNull(form.getId())) {
            deviceAttributionDO.setLogoPic(userDefaultPic);
        } else if (StrUtil.isNotBlank(form.getBase64())) {
            deviceAttributionDO.setLogoPic(fileServiceProxy.uploadByBase64(form.getBase64()));
        }

        deviceAttributionDO.setUid(uId);
        deviceAttributionService.saveOrUpdate(deviceAttributionDO);
    }

    /**
     * 归属方code校验
     *
     * @param form
     * @param uId
     */
    private void repeatCheck(DeviceAttributionForm form, String uId) {
        int preSize = StrUtil.split(form.getCode(), ",").size();
        int size = (int) StrUtil.split(form.getCode(), ",").stream().distinct().count();

        if (preSize != size) {
            throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_CANNOT_REPEAT);
        }

        List<DeviceAttributionDO> attributionDOList = new ArrayList<>();
        if (form.getId() != null) {
            attributionDOList = deviceAttributionService.lambdaQuery()
                    .eq(DeviceAttributionDO::getUid, uId)
                    .ne(BaseDO::getId, form.getId())
                    .list();
        } else {
            attributionDOList = deviceAttributionService.lambdaQuery()
                    .eq(DeviceAttributionDO::getUid, uId)
                    .list();
        }

        if (CollUtil.isNotEmpty(attributionDOList)) {
            List<String> dbCodes = new ArrayList<>();

            attributionDOList.forEach(e -> {
                List<String> split = StrUtil.split(e.getCode(), ",");
                dbCodes.addAll(split);
            });

            StrUtil.split(form.getCode(), ",").forEach(e -> {
                if (dbCodes.contains(e)) {
                    throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_CANNOT_REPEAT);
                }
            });
        }
    }

    @Override
    public void delete(Long id) {
        DeviceAttributionDO one = deviceAttributionService.lambdaQuery()
                .eq(DeviceAttributionDO::getId, id)
                .one();
        if (ObjectUtil.isNull(one)) {
            return;
        }
        DeviceBindingDO bindingDO = deviceBindingService.lambdaQuery()
                .eq(DeviceBindingDO::getAttributionId, id)
                .one();
        if (ObjectUtil.isNotNull(bindingDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_HAS_DEVICE);
        }
        List<WeighDataDO> list = weighDataService.lambdaQuery()
                .eq(WeighDataDO::getAttributionId, id)
                .list();
        if (CollUtil.isNotEmpty(list)) {
            throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_HAS_DATA);
        }
        deviceAttributionService.lambdaUpdate()
                .eq(DeviceAttributionDO::getId, id)
                .set(DeviceAttributionDO::getDeleted, 1)
                .update();
    }

    @Override
    public List<AttributionDeviceVO> configList(String type, Long attributionId) {
        return deviceAttributionExtMapper.listAttributionDevice(userIdUtil.getUId(), type, attributionId);
    }
}
