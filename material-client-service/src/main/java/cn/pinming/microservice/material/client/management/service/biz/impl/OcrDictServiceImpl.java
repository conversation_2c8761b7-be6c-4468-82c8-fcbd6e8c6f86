package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.enums.RoleEnum;
import cn.pinming.microservice.material.client.management.common.form.OCRDictForm;
import cn.pinming.microservice.material.client.management.common.mapper.OcrDictMapper;
import cn.pinming.microservice.material.client.management.common.model.OcrDictDO;
import cn.pinming.microservice.material.client.management.common.query.OCRDictQuery;
import cn.pinming.microservice.material.client.management.common.vo.OCRDictVO;
import cn.pinming.microservice.material.client.management.infrastructure.annotation.RoleAuthority;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.service.biz.IOcrDictService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * OCR模版字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Service
public class OcrDictServiceImpl extends ServiceImpl<OcrDictMapper, OcrDictDO> implements IOcrDictService {

    @RoleAuthority(value = RoleEnum.ADMIN)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveDict(OCRDictForm form) {
        String name = form.getName();
        String value = form.getValue();

        // 查询是否存在相同的name
        int count = lambdaQuery().eq(OcrDictDO::getName, name).count();
        if (count != 0) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "字典名称已存在");
        }
        // 查询是否存在相同的value
        count = lambdaQuery().eq(OcrDictDO::getValue, value).count();
        if (count != 0) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "字典值已存在");
        }
        OcrDictDO ocrDictDO = new OcrDictDO();
        ocrDictDO.setName(name);
        ocrDictDO.setValue(value);
        save(ocrDictDO);
    }

    @RoleAuthority(value = RoleEnum.ADMIN)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteDict(Long id) {
        removeById(id);
    }


    @RoleAuthority(value = RoleEnum.ADMIN)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDict(OCRDictForm form) {
        Long id = form.getId();
        String name = form.getName();
        String value = form.getValue();
        if (id == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "字典ID为空");
        }
        Integer count = lambdaQuery()
                .ne(OcrDictDO::getId, id)
                .and(wrapper -> wrapper.eq(OcrDictDO::getName, name).or().eq(OcrDictDO::getValue, value))
                .count();

        if (count != 0) {
             throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "字典名/值已存在,请修改");
        }

        OcrDictDO ocrDictDO = new OcrDictDO();
        BeanUtils.copyProperties(form, ocrDictDO);
        updateById(ocrDictDO);
    }



    @Override
    public Page<OCRDictVO> pageByQuery(OCRDictQuery query) {
        return getBaseMapper().pageByQuery(query);
    }
}
