package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.material.v2.model.dto.PicUrlDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataCheckDTO;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.form.oss.FileConfirmForm;
import cn.pinming.microservice.material.client.management.common.form.oss.FileStsForm;
import cn.pinming.microservice.material.client.management.common.form.oss.MultiFileStsForm;
import cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO;
import cn.pinming.microservice.material.client.management.common.vo.oss.FileOssConfigVO;
import cn.pinming.microservice.material.client.management.common.vo.oss.StsResponseVO;
import cn.pinming.microservice.material.client.management.service.biz.FileOssService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataPicService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.DownloadUrlOptionsDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.FileIdentityDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.StorageProviderConfigDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.sts.FileStsFormDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.sts.FileStsFormOptionsDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.sts.PolicyInfDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.sts.StsResponseDto;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/6/16 15:31
 */
@Slf4j
@Service
public class FileOssServiceImpl implements FileOssService {

    @DubboReference
    private FileCenterService fileCenterService;
    @Resource
    private WeighDataService weighDataService;
    @Resource
    private WeighDataPicService weighDataPicService;

    @Override
    public FileOssConfigVO getConfig(MultiFileStsForm form) {
        WeighDataCheckDTO check = weighDataService.check(form.getSn(), DeveloperAppEnum.UPLOAD.value(), true, null);
        FileStsFormOptionsDto optionsDto = new FileStsFormOptionsDto();
        optionsDto.setMId(check.getUid());
        optionsDto.setApplyPolicy(true);
        optionsDto.setAutoConfirm(true);
        List<FileStsFormDto> forms = BeanUtil.copyToList(form.getStslist(), FileStsFormDto.class);
        StsResponseDto stsResponseDto = fileCenterService.applyUploadSts(optionsDto, forms);
        StorageProviderConfigDto storageProviderConfig = fileCenterService.findStorageProviderConfig(null);
        return FileOssConfigVO.builder()
                .stsResponseDto(stsResponseDto)
                .storageProviderConfig(storageProviderConfig)
                .uid(check.getUid())
                .attributionId(check.getAttributionId())
                .build();
    }

    @Override
    public StorageProviderConfigDto getConfig(Byte subSystem) {
        return fileCenterService.findStorageProviderConfig(subSystem);
    }

    @Override
    public void confirm(FileConfirmForm form) {
        weighDataService.check(form.getSn(), DeveloperAppEnum.UPLOAD.value(), true, null);
        fileCenterService.addFileUuidsToFileServer(form.getFileId());
    }

    @Override
    public void confirmList(List<String> fileUuids) {
        fileCenterService.addFileUuidsToFileServer(fileUuids);
    }

    @Override
    public String getUrlByUuid(String uuid) {
        DownloadUrlOptionsDto optionsDto = new DownloadUrlOptionsDto();
        optionsDto.setPreviewUrl(true);
        FileIdentityDto fileIdentityDto = new FileIdentityDto();
        fileIdentityDto.setFileUuid(uuid);
        return fileCenterService.acquireFileDownloadUrl(fileIdentityDto, optionsDto);
    }

    @Override
    public List<String> getUrlByUuids(List<String> uuids) {
        Map<String, String> map = fileCenterService.simpleAcquireFileDownloadUrls(uuids);
        if (CollUtil.isNotEmpty(map)) {
            return new ArrayList<>(map.values());
        } else return null;
    }

    @Override
    public Map<String, String> getUrlMapByUuids(List<String> uuids) {
        return fileCenterService.simpleAcquireFileDownloadUrls(uuids);
    }


    @Override
    public String getPicUrlByUuid(String uuid, Byte picType) {
        FileIdentityDto fileIdentityDto = new FileIdentityDto();
        fileIdentityDto.setFileUuid(uuid);
        DownloadUrlOptionsDto optionsDto = new DownloadUrlOptionsDto();
        optionsDto.setPicType(picType);
        return fileCenterService.acquireFileDownloadUrl(fileIdentityDto, optionsDto);
    }

    @Override
    public long spaceStatistics(String uid) {
        FileStsFormOptionsDto optionsDto = new FileStsFormOptionsDto();
        optionsDto.setMId(uid);
        optionsDto.setApplyPolicy(true);
        optionsDto.setAutoConfirm(true);
        StsResponseDto stsResponseDto = fileCenterService.applyUploadSts(optionsDto, new ArrayList<>());
        StorageProviderConfigDto storageProviderConfig = fileCenterService.findStorageProviderConfig(null);

        if (stsResponseDto != null && storageProviderConfig != null) {
            String bucketName = storageProviderConfig.getBucket();
            String endpoint = storageProviderConfig.getEndPoint();
            String accessKeyId = stsResponseDto.getPolicyInf().getAccessKeyId();
            String accessKeySecret = stsResponseDto.getPolicyInf().getAccessKeySecret();
            String securityToken = stsResponseDto.getPolicyInf().getSecurityToken();
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, securityToken);
            String keyPrefix = uid + "/" + LocalDate.now().toString();
            long spaceSize = 0;
            try {
                ObjectListing objectListing = null;
                do {
                    // 默认情况下，每次列举100个文件或目录。
                    ListObjectsRequest request = new ListObjectsRequest(bucketName).withDelimiter("/").withPrefix(keyPrefix).withMaxKeys(500);
                    if (objectListing != null) {
                        request.setMarker(objectListing.getNextMarker());
                    }
                    objectListing = ossClient.listObjects(request);
                    List<String> folders = objectListing.getCommonPrefixes();
                    spaceSize += folders.stream().mapToLong(folder -> calculateFolderLength(ossClient, bucketName, folder) / 1024).sum();

                    List<OSSObjectSummary> sums = objectListing.getObjectSummaries();
                    spaceSize += sums.stream().mapToLong(s -> s.getSize() / 1024).sum();
                } while (objectListing.isTruncated());
            } catch (OSSException | ClientException oe) {
                oe.printStackTrace();
            } finally {
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            }
            return spaceSize;
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(String fileId) {
        if (StrUtil.isNotBlank(fileId)) {
            WeighDataPicDO picDO = weighDataPicService.lambdaQuery().eq(WeighDataPicDO::getFileId, fileId).one();
            if (picDO != null) {
                FileStsFormOptionsDto optionsDto = new FileStsFormOptionsDto();
                optionsDto.setMId(picDO.getUid());
                optionsDto.setApplyPolicy(true);
                optionsDto.setAutoConfirm(true);
                StsResponseDto stsResponseDto = fileCenterService.applyUploadSts(optionsDto, new ArrayList<>());
                StorageProviderConfigDto storageProviderConfig = fileCenterService.findStorageProviderConfig(null);
                PolicyInfDto policyInf = stsResponseDto.getPolicyInf();
                String accessKeyId = policyInf.getAccessKeyId();
                String accessKeySecret = policyInf.getAccessKeySecret();
                String securityToken = policyInf.getSecurityToken();
                String endPoint = storageProviderConfig.getEndPoint();
                String bucketName = storageProviderConfig.getBucket();
                OSS ossClient = new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, securityToken);
                String key = StrUtil.format("{}/{}/{}.jpg", picDO.getUid(), picDO.getLocalCTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), picDO.getFileId());
                boolean exist = ossClient.doesObjectExist(bucketName, key);
                if (exist) {
                    ossClient.deleteObject(bucketName, key);
                }
                weighDataPicService.removeById(picDO.getId());
            }
        }
    }

    @Override
    public StsResponseVO getFileSts(FileStsForm form) {
        FileStsFormOptionsDto optionsDto = new FileStsFormOptionsDto();
        optionsDto.setMId("系统管理员");
        optionsDto.setApplyPolicy(true);
        optionsDto.setAutoConfirm(true);
        List<FileStsFormDto> forms = BeanUtil.copyToList(Collections.singletonList(form), FileStsFormDto.class);
        StsResponseDto stsResponseDto = fileCenterService.applyUploadSts(optionsDto, forms);
        StsResponseVO result = new StsResponseVO();
        result.setPolicyInf(stsResponseDto.getPolicyInf());
        result.setFileList(stsResponseDto.getStsFiles());
        return result;
    }

    @Override
    public List<String> getUrlByUuidAndTime(List<String> uuids, Date date, Boolean isPre) {
        if (CollUtil.isEmpty(uuids)) {
            return null;
        }
        DownloadUrlOptionsDto options = new DownloadUrlOptionsDto();

        List<FileIdentityDto> fileIdentities = uuids.stream().map(e -> {
            FileIdentityDto dto = new FileIdentityDto();
            dto.setFileUuid(e);
            return dto;
        }).collect(Collectors.toList());
        options.setTime(date);
        if (isPre) {
            options.setPreviewUrl(true);
        }

        Map<FileIdentityDto, String> fileIdentityDtoStringMap = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);
        if (CollUtil.isNotEmpty(fileIdentityDtoStringMap)) {
            return fileIdentityDtoStringMap.values().stream().collect(Collectors.toList());
        } else return null;
    }

    @Override
    public Map<String, String> getUrlByUuidAndTimeToMap(List<String> uuids, Date date, Boolean isPre) {
        if (CollUtil.isEmpty(uuids)) {
            return null;
        }
        Map<String, String> result = new HashMap<>();
        DownloadUrlOptionsDto options = new DownloadUrlOptionsDto();

        List<FileIdentityDto> fileIdentities = uuids.stream().map(e -> {
            FileIdentityDto dto = new FileIdentityDto();
            dto.setFileUuid(e);
            return dto;
        }).collect(Collectors.toList());
        options.setTime(date);
        if (isPre) {
            options.setPreviewUrl(true);
        }

        Map<FileIdentityDto, String> fileIdentityDtoStringMap = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);
        if (CollUtil.isNotEmpty(fileIdentityDtoStringMap)) {
            fileIdentityDtoStringMap.forEach((k, v) -> {
                result.put(k.getFileUuid(), v);
            });
        }
        return result;
    }


    @Override
    public List<PicUrlDTO> getPicUrlDTOs(List<String> uuids) {
        if (CollUtil.isEmpty(uuids)) {
            return null;
        }
        List<FileIdentityDto> fileIdentities = uuids.stream().map(e -> {
            FileIdentityDto dto = new FileIdentityDto();
            dto.setFileUuid(e);
            return dto;
        }).collect(Collectors.toList());

        DownloadUrlOptionsDto options = new DownloadUrlOptionsDto();
        options.setTime(Date.from(LocalDate.now().plusYears(10).atStartOfDay(ZoneId.systemDefault()).toInstant()));//10年有效
        options.setPreviewUrl(false);//下载地址
        Map<FileIdentityDto, String> downloadDtoMap = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);
        if (CollUtil.isEmpty(downloadDtoMap)) {
            return null;
        }
        Map<String, PicUrlDTO> resultMap = downloadDtoMap.entrySet().stream().collect(Collectors.toMap(e -> e.getKey().getFileUuid(), e -> {
            PicUrlDTO picUrlDTO = new PicUrlDTO();
            picUrlDTO.setFileUUid(e.getKey().getFileUuid());
            picUrlDTO.setDownloadUrl(e.getValue());
            return picUrlDTO;
        }));
        options.setPreviewUrl(true);//预览地址
        Map<FileIdentityDto, String> previewDtoMap = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);
        if (CollUtil.isEmpty(previewDtoMap)) {
            return new ArrayList<>(resultMap.values());
        }
        previewDtoMap.forEach((k, v) -> {
            if (resultMap.containsKey(k.getFileUuid())) {
                PicUrlDTO picUrlDTO = resultMap.get(k.getFileUuid());
                picUrlDTO.setPreviewUrl(v);
            }
        });
        return new ArrayList<>(resultMap.values());
    }

    private static long calculateFolderLength(OSS ossClient, String bucketName, String folder) {
        long size = 0L;
        ObjectListing objectListing = null;
        do {
            // MaxKey默认值为100，最大值为1000。
            ListObjectsRequest request = new ListObjectsRequest(bucketName).withPrefix(folder).withMaxKeys(1000);
            if (objectListing != null) {
                request.setMarker(objectListing.getNextMarker());
            }
            objectListing = ossClient.listObjects(request);
            List<OSSObjectSummary> sums = objectListing.getObjectSummaries();
            for (OSSObjectSummary s : sums) {
                size += s.getSize();
            }
        } while (objectListing.isTruncated());
        return size;
    }


}
