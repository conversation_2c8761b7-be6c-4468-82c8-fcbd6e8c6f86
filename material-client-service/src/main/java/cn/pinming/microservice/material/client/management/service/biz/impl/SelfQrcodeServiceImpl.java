package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.form.supplier.SelfQrcodeEditForm;
import cn.pinming.microservice.material.client.management.common.form.supplier.SelfQrcodeForm;
import cn.pinming.microservice.material.client.management.common.mapper.SelfQrcodeMapper;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.common.vo.SelfQrcodeVO;
import cn.pinming.microservice.material.client.management.common.vo.SupplierConfigVO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.service.biz.*;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 磅房自助运单入口码管理设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Service
public class SelfQrcodeServiceImpl extends ServiceImpl<SelfQrcodeMapper, SelfQrcodeDO> implements ISelfQrcodeService {

    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private DeviceBindingService deviceBindingService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private SupplierConfigService supplierConfigService;

    @NacosValue(value = "${h5.self.qrcode.url}", autoRefreshed = true)
    private String selfQRCodeUrl;
    @NacosValue(value = "${h5.ocr.qrcode.url}", autoRefreshed = true)
    private String ocrQRCodeUrl;

    @Override
    public List<SelfQrcodeVO> listConfig(Integer type) {
        List<SelfQrcodeVO> list = getBaseMapper().selectListConfig(userIdUtil.getUId(), type);
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                String baseUrl = type == 1 ? selfQRCodeUrl : ocrQRCodeUrl;
                item.setUrl(StrUtil.format(baseUrl, item.getBizId(), item.getAttributionId()));
                if (StrUtil.isNotBlank(item.getSupplierId())) {
                    item.setSupplierIdList(StrUtil.split(item.getSupplierId(), StrUtil.COMMA).stream().map(Long::parseLong).collect(Collectors.toList()));
                }
            });
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(SelfQrcodeForm form) {

        String name = form.getName();
        String uid = userIdUtil.getUId();
        Integer count = lambdaQuery().eq(SelfQrcodeDO::getName, name).eq(SelfQrcodeDO::getCreateId, uid)
                .eq(SelfQrcodeDO::getType, form.getType())
                .count();
        if (count > 0) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "入口名称已存在");
        }

        Integer type = form.getType();
        // 类型为ocr时 校验归属方是否已被使用
        if (type == 2) {
            count = lambdaQuery().eq(SelfQrcodeDO::getAttributionId, form.getAttributionId())
                    .eq(SelfQrcodeDO::getCreateId, uid)
                    .eq(SelfQrcodeDO::getType, type).count();
            if (count > 0) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方已被使用");
            }
        }

        SelfQrcodeDO selfQrcodeDO = new SelfQrcodeDO();
        BeanUtils.copyProperties(form, selfQrcodeDO);
        selfQrcodeDO.setBizId(IdUtil.fastSimpleUUID());
        save(selfQrcodeDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableById(Long id) {
        getBaseMapper().updateEnableStatusById(id, userIdUtil.getUId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(SelfQrcodeEditForm form) {
        SelfQrcodeDO selfQrcodeDO = getById(form.getId());
        if (selfQrcodeDO == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "入口码不存在");
        }
        if (!selfQrcodeDO.getCreateId().equals(userIdUtil.getUId())) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "入口码不属于当前用户");
        }
        BeanUtils.copyProperties(form, selfQrcodeDO);
        selfQrcodeDO.setSupplierId(StrUtil.join(StrUtil.COMMA, form.getSupplierIds()));
        updateById(selfQrcodeDO);
    }

    @Override
    public List<SupplierConfigVO> listSupplierConfig(String bizId) {
        SelfQrcodeDO selfQrcode = lambdaQuery().eq(SelfQrcodeDO::getBizId, bizId).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "入口二维码错误"));
        Byte enabled = selfQrcode.getEnabled();
        if (enabled == 1) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "入口码已禁用");
        }
        String supplierId = selfQrcode.getSupplierId();
        Long attributionId = selfQrcode.getAttributionId();
        DeviceAttributionDO attribution = deviceAttributionService.getById(attributionId);
        if (attribution == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方信息不存在");
        }

        if (StrUtil.isNotBlank(supplierId)) {
            List<Long> supplierIdList = StrUtil.split(supplierId, StrUtil.COMMA).stream().map(Long::parseLong).collect(Collectors.toList());
            //供应商排序：按当前归属方所在该供应商的最新订单要货时间降序。
            return getBaseMapper().selectSupplierList(supplierIdList, attributionId);
        }
        return Collections.emptyList();
    }

    @Override
    public List<SupplierConfigVO> listSupplierConfigByDeviceSn(String deviceSn, String deviceType) {
        DeviceDO deviceDO = deviceService.lambdaQuery().eq(DeviceDO::getDeviceSn, deviceSn).eq(StrUtil.isNotBlank(deviceType), DeviceDO::getDeviceType, deviceType).oneOpt()
                .orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "设备不存在"));
        DeviceBindingDO deviceBindingDO = deviceBindingService.lambdaQuery().eq(DeviceBindingDO::getDeviceId, deviceDO.getId()).oneOpt()
                .orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "设备未绑定归属方"));
        DeviceAttributionDO attribution = deviceAttributionService.getById(deviceBindingDO.getAttributionId());
        if (attribution == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方信息不存在");
        }

        List<SupplierConfigDO> list = supplierConfigService.lambdaQuery().eq(SupplierConfigDO::getCreateId, attribution.getUid()).list();
//        List<SelfQrcodeDO> list = lambdaQuery().eq(SelfQrcodeDO::getAttributionId, attribution.getId()).eq(SelfQrcodeDO::getType, 1)
//                .eq(SelfQrcodeDO::getEnabled, 0).list();
        if (CollUtil.isNotEmpty(list)) {
            List<Long> supplierIdList = list.stream().map(SupplierConfigDO::getId).collect(Collectors.toList());
            return getBaseMapper().selectSupplierList(supplierIdList, deviceBindingDO.getAttributionId());
        }
        return Collections.emptyList();
    }

    @Override
    public List<DeviceAttributionDO> attributeList() {
        String uid = userIdUtil.getUId();
        List<DeviceAttributionDO> result = deviceAttributionService.lambdaQuery()
                .eq(DeviceAttributionDO::getUid, uid)
                .orderByDesc(BaseDO::getGmtCreate)
                .list();
        List<SelfQrcodeVO> list = getBaseMapper().selectListConfig(uid, 2);
        if (CollUtil.isNotEmpty(result) && CollUtil.isNotEmpty(list)) {
            Set<Long> attributionIdSet = list.stream().map(SelfQrcodeVO::getAttributionId).collect(Collectors.toSet());
            result.forEach(item -> item.setUsed(attributionIdSet.contains(item.getId())));
        }
        return result;
    }

    @Override
    public TreeMap<String, List<SupplierConfigVO>> supplierConfigMapByDeviceSnAndType(String deviceSn, String deviceType) {
        DeviceDO deviceDO = deviceService.lambdaQuery().eq(DeviceDO::getDeviceSn, deviceSn).eq(StrUtil.isNotBlank(deviceType), DeviceDO::getDeviceType, deviceType).oneOpt()
                .orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "设备不存在"));
        DeviceBindingDO deviceBindingDO = deviceBindingService.lambdaQuery().eq(DeviceBindingDO::getDeviceId, deviceDO.getId()).oneOpt()
                .orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "设备未绑定归属方"));
        DeviceAttributionDO attribution = deviceAttributionService.getById(deviceBindingDO.getAttributionId());
        if (attribution == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方信息不存在");
        }

        List<SupplierConfigDO> list = supplierConfigService.lambdaQuery().eq(SupplierConfigDO::getCreateId, attribution.getCreateId()).list();
        TreeMap<String, List<SupplierConfigVO>> result = new TreeMap<>();
        if (CollUtil.isNotEmpty(list)) {
            List<Long> supplierIdList = list.stream().map(SupplierConfigDO::getId).collect(Collectors.toList());
            List<SupplierConfigVO> configVOList = getBaseMapper().selectSupplierList(supplierIdList, deviceBindingDO.getAttributionId());
            Set<String> groupSet = configVOList.stream()
                    .map(SupplierConfigVO::getGroup)
                    .flatMap(group -> Arrays.stream(group.split(StrUtil.COMMA)))
                    .collect(Collectors.toSet());
            configVOList.forEach(obj->obj.setAttributionId(attribution.getId()));
            result.put("所有", configVOList);
            for (String group : groupSet) {
                List<SupplierConfigVO> groupList = configVOList.stream()
                        .filter(supplier -> supplier.getGroup().contains(group)).collect(Collectors.toList());
                result.put(group, groupList);
            }
        }
        return result;
    }
}
