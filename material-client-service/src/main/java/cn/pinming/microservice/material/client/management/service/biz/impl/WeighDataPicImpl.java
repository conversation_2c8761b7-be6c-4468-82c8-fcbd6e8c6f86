package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.client.management.common.mapper.WeighDataPicMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataPicExtMapper;
import cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO;
import cn.pinming.microservice.material.client.management.service.biz.FileOssService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataPicService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WeighDataPicImpl extends ServiceImpl<WeighDataPicMapper, WeighDataPicDO> implements WeighDataPicService {

    @Resource
    private WeighDataPicExtMapper weighDataPicExtMapper;
    @Resource
    private FileOssService fileOssService;

    @Override
    public long queryTodayUsedSpaceByUid(String uid) {
        return weighDataPicExtMapper.selectTodayUsedSpace(uid);
    }

    /**
     * 得到图片地址
     *
     * @param isPre true 预览地址  false 真实地址
     */
    @Override
    public Map<String, List<String>> getPic(List<String> recordList, Boolean isPre) {
        Map<String, List<String>> map = new HashMap<>();
        List<WeighDataPicDO> list = this.lambdaQuery().in(WeighDataPicDO::getRecordId, recordList).groupBy(WeighDataPicDO::getFilePath).list();
        if (CollUtil.isNotEmpty(list)) {
            Map<String, List<WeighDataPicDO>> collect = list.stream().collect(Collectors.groupingBy(WeighDataPicDO::getRecordId));
            LocalDate currentDate = LocalDate.now();
            // 将当前日期增加 10 年
            LocalDate futureDate = currentDate.plusYears(10);
            Date date = Date.from(futureDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            collect.forEach((k, v) -> {
                List<String> uuids = v.stream().map(WeighDataPicDO::getFileId).collect(Collectors.toList());
                List<String> pics = fileOssService.getUrlByUuidAndTime(uuids, date, isPre);
                map.put(k, pics);
            });
        }
        return map;
    }

    @Override
    public Map<String, List<String>> getUuid(List<String> recordList) {
        Map<String, List<String>> map = new HashMap<>();
        List<WeighDataPicDO> list = this.lambdaQuery().in(WeighDataPicDO::getRecordId, recordList).groupBy(WeighDataPicDO::getFilePath).list();
        if (CollUtil.isNotEmpty(list)) {
            Map<String, List<WeighDataPicDO>> collect = list.stream().collect(Collectors.groupingBy(WeighDataPicDO::getRecordId));
            collect.forEach((k, v) -> {
                List<String> uuids = v.stream().map(WeighDataPicDO::getFileId).collect(Collectors.toList());
                map.put(k,uuids);
            });
        }
        return map;
    }
}
