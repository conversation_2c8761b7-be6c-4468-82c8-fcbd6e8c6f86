package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.mapper.OcrResultMapper;
import cn.pinming.microservice.material.client.management.common.model.OcrResultDO;
import cn.pinming.microservice.material.client.management.service.biz.OCRResultService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OCRResultServiceImpl extends ServiceImpl<OcrResultMapper, OcrResultDO> implements OCRResultService {
}
