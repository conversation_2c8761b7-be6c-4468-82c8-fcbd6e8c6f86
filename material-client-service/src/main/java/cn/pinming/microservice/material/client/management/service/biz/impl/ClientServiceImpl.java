package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.client.management.common.enums.ClientTypeEnum;
import cn.pinming.microservice.material.client.management.common.form.client.ClientForm;
import cn.pinming.microservice.material.client.management.common.mapper.ClientMapper;
import cn.pinming.microservice.material.client.management.common.model.ClientDO;
import cn.pinming.microservice.material.client.management.common.vo.ClientVO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.service.biz.ClientService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/9/13 10:49
 */
@Service
public class ClientServiceImpl extends ServiceImpl<ClientMapper, ClientDO> implements ClientService {

    @Resource
    private ClientMapper clientExtMapper;
    @DubboReference
    private FileCenterService fileCenterService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void upload(ClientForm form) {
        // 校验版本号是否已存在
        String version = form.getVersion().trim();
        // 字符串需符合x.x.x格式
        if (!version.matches("\\d+\\.\\d+\\.\\d+")) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(),"版本号格式错误，请按照x.x.x格式填写");
        }

        Byte type = form.getType();
        Integer count = this.lambdaQuery().eq(ClientDO::getVersion, version).eq(ClientDO::getType, type).count();
        if (count > 0) {
            throw new BizErrorException(BizExceptionMessageEnum.VERSION_EXIST);
        }
        fileCenterService.addFileUuidsToFileServer(Collections.singletonList(form.getFileId()));
        ClientDO clientDO = new ClientDO();
        BeanUtils.copyProperties(form, clientDO);
        this.save(clientDO);
    }

    @Override
    public String download(String fileId) {
        return fileCenterService.simpleAcquireFileDownloadUrl(fileId);
    }

    @Override
    public IPage<ClientVO> clientPageList(Page query) {
        IPage<ClientVO> page = clientExtMapper.selectPageList(query);
        List<ClientVO> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<String> fileIds = records.stream().map(ClientVO::getFileId).collect(Collectors.toList());
            Map<String, String> urlMap = fileCenterService.simpleAcquireFileDownloadUrls(fileIds);
            if (urlMap != null && !urlMap.isEmpty()) {
                for (ClientVO record : records) {
                    record.setUrl(urlMap.getOrDefault(record.getFileId(), null));
                }
            }
            records.forEach(obj -> obj.setTypeName(ClientTypeEnum.KEY_MAP.get(obj.getType())));
        }
        return page;
    }

    @Override
    public ClientVO clientLatest(Byte type) {
        ClientVO result = new ClientVO();
        ClientDO clientDO = this.lambdaQuery().eq(ClientDO::getType, type).orderByDesc(ClientDO::getVersion).last("LIMIT 1").one();
        if (clientDO != null) {
            BeanUtils.copyProperties(clientDO, result);
            result.setTypeName(ClientTypeEnum.KEY_MAP.get(result.getType()));
            result.setUrl(fileCenterService.simpleAcquireFileDownloadUrl(result.getFileId()));
        }
        return result;
    }
}
